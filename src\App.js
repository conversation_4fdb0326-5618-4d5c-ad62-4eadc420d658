/**
 * MeshTalk - Main Application Component
 * Entry point for the decentralized mesh messaging app
 */

import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Alert,
  AppState,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Toast from 'react-native-toast-message';

// Core Services
import MeshManager from '@bluetooth/MeshManager';
import MessageHandler from '@messaging/MessageHandler';
import MessageStore from '@storage/MessageStore';
import PanicMode from '@storage/PanicMode';

// UI Components
import ChatScreen from '@components/ChatScreen';
import RoomList from '@components/RoomList';
import NetworkTopology from '@components/NetworkTopology';
import SettingsScreen from '@components/SettingsScreen';

// Utils
import Logger from '@utils/Logger';
import {APP_CONFIG} from '@constants/Config';

const Tab = createBottomTabNavigator();

const App = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [meshManager, setMeshManager] = useState(null);
  const [messageHandler, setMessageHandler] = useState(null);
  const [messageStore, setMessageStore] = useState(null);
  const [panicMode, setPanicMode] = useState(null);
  const [appState, setAppState] = useState(AppState.currentState);

  useEffect(() => {
    initializeApp();
    
    // Setup app state listener
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      appStateSubscription?.remove();
      shutdownApp();
    };
  }, []);

  /**
   * Initialize the application
   */
  const initializeApp = async () => {
    try {
      Logger.info('App: Initializing MeshTalk...');
      
      // Initialize core services
      const meshMgr = new MeshManager();
      const msgHandler = new MessageHandler();
      const msgStore = new MessageStore();
      const panic = new PanicMode();
      
      // Initialize panic mode first
      await panic.initialize();
      
      // Register cleanup callbacks for panic mode
      panic.registerCleanupCallback('MessageStore', () => msgStore.emergencyCleanup());
      panic.registerCleanupCallback('MessageHandler', () => msgHandler.emergencyCleanup());
      panic.registerCleanupCallback('MeshManager', () => meshMgr.shutdown());
      
      // Initialize other services
      await msgStore.initialize();
      await msgHandler.initialize();
      await meshMgr.initialize();
      
      // Setup event listeners
      setupEventListeners(meshMgr, msgHandler, msgStore, panic);
      
      // Store service instances
      setMeshManager(meshMgr);
      setMessageHandler(msgHandler);
      setMessageStore(msgStore);
      setPanicMode(panic);
      
      setIsInitialized(true);
      
      Logger.info('App: MeshTalk initialized successfully');
      
      // Show welcome toast
      Toast.show({
        type: 'success',
        text1: 'MeshTalk Ready',
        text2: 'Decentralized messaging is now active',
        visibilityTime: 3000,
      });
      
    } catch (error) {
      Logger.error('App: Initialization failed', error);
      
      Alert.alert(
        'Initialization Error',
        'Failed to initialize MeshTalk. Please restart the app.',
        [
          {
            text: 'Retry',
            onPress: initializeApp,
          },
          {
            text: 'Exit',
            onPress: () => {
              // In a real app, you might want to exit gracefully
            },
            style: 'destructive',
          },
        ]
      );
    }
  };

  /**
   * Setup event listeners for core services
   */
  const setupEventListeners = (meshMgr, msgHandler, msgStore, panic) => {
    // Mesh network events
    meshMgr.on('deviceDiscovered', (device) => {
      Logger.info(`App: Device discovered: ${device.name} (${device.id})`);
      Toast.show({
        type: 'info',
        text1: 'Device Found',
        text2: `${device.name} is nearby`,
        visibilityTime: 2000,
      });
    });
    
    meshMgr.on('deviceConnected', (device) => {
      Logger.info(`App: Device connected: ${device.name} (${device.id})`);
      Toast.show({
        type: 'success',
        text1: 'Device Connected',
        text2: `Connected to ${device.name}`,
        visibilityTime: 2000,
      });
    });
    
    meshMgr.on('deviceDisconnected', (deviceId) => {
      Logger.info(`App: Device disconnected: ${deviceId}`);
      Toast.show({
        type: 'info',
        text1: 'Device Disconnected',
        text2: 'A device left the mesh',
        visibilityTime: 2000,
      });
    });
    
    // Message events
    msgHandler.on('messageReceived', ({message, fromDeviceId}) => {
      Logger.info(`App: Message received from ${fromDeviceId}`);
      
      // Store message
      msgStore.storeMessage(message);
      
      // Show notification for text messages
      if (message.type === 'text' && message.content) {
        Toast.show({
          type: 'info',
          text1: 'New Message',
          text2: message.content.substring(0, 50) + (message.content.length > 50 ? '...' : ''),
          visibilityTime: 4000,
        });
      }
    });
    
    msgHandler.on('messageError', ({message, error}) => {
      Logger.error(`App: Message error for ${message.id}`, error);
      Toast.show({
        type: 'error',
        text1: 'Message Error',
        text2: 'Failed to send message',
        visibilityTime: 3000,
      });
    });
    
    // Panic mode events
    panic.on('panicActivated', () => {
      Logger.emergency('App: PANIC MODE ACTIVATED');
      Toast.show({
        type: 'error',
        text1: '🚨 PANIC MODE',
        text2: 'Emergency cleanup in progress...',
        visibilityTime: 5000,
      });
    });
    
    panic.on('panicCompleted', () => {
      Logger.emergency('App: Panic mode cleanup completed');
      Toast.show({
        type: 'info',
        text1: '🔒 Cleanup Complete',
        text2: 'All data has been securely deleted',
        visibilityTime: 5000,
      });
    });
  };

  /**
   * Handle app state changes
   */
  const handleAppStateChange = (nextAppState) => {
    Logger.info(`App: State changed from ${appState} to ${nextAppState}`);
    
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      Logger.info('App: Resumed from background');
    } else if (nextAppState.match(/inactive|background/)) {
      // App has gone to the background
      Logger.info('App: Moved to background');
    }
    
    setAppState(nextAppState);
  };

  /**
   * Handle tap events for panic mode
   */
  const handleTap = () => {
    if (panicMode) {
      panicMode.handleTap();
    }
  };

  /**
   * Shutdown the application
   */
  const shutdownApp = async () => {
    try {
      Logger.info('App: Shutting down MeshTalk...');
      
      if (panicMode) await panicMode.shutdown();
      if (messageStore) await messageStore.shutdown();
      if (messageHandler) await messageHandler.shutdown();
      if (meshManager) await meshManager.shutdown();
      
      Logger.info('App: Shutdown complete');
    } catch (error) {
      Logger.error('App: Shutdown failed', error);
    }
  };

  if (!isInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1a1a1a" />
        <View style={styles.loadingContainer}>
          {/* Loading component would go here */}
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} onTouchEnd={handleTap}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a1a" />
      
      <NavigationContainer>
        <Tab.Navigator
          screenOptions={{
            tabBarStyle: styles.tabBar,
            tabBarActiveTintColor: '#00ff88',
            tabBarInactiveTintColor: '#666',
            headerStyle: styles.header,
            headerTintColor: '#fff',
            headerTitleStyle: styles.headerTitle,
          }}
        >
          <Tab.Screen
            name="Chat"
            options={{
              title: 'Messages',
              tabBarIcon: ({color}) => (
                <View style={[styles.tabIcon, {backgroundColor: color}]} />
              ),
            }}
          >
            {(props) => (
              <ChatScreen
                {...props}
                meshManager={meshManager}
                messageHandler={messageHandler}
                messageStore={messageStore}
              />
            )}
          </Tab.Screen>
          
          <Tab.Screen
            name="Rooms"
            options={{
              title: 'Rooms',
              tabBarIcon: ({color}) => (
                <View style={[styles.tabIcon, {backgroundColor: color}]} />
              ),
            }}
          >
            {(props) => (
              <RoomList
                {...props}
                meshManager={meshManager}
                messageHandler={messageHandler}
              />
            )}
          </Tab.Screen>
          
          <Tab.Screen
            name="Network"
            options={{
              title: 'Network',
              tabBarIcon: ({color}) => (
                <View style={[styles.tabIcon, {backgroundColor: color}]} />
              ),
            }}
          >
            {(props) => (
              <NetworkTopology
                {...props}
                meshManager={meshManager}
              />
            )}
          </Tab.Screen>
          
          <Tab.Screen
            name="Settings"
            options={{
              title: 'Settings',
              tabBarIcon: ({color}) => (
                <View style={[styles.tabIcon, {backgroundColor: color}]} />
              ),
            }}
          >
            {(props) => (
              <SettingsScreen
                {...props}
                meshManager={meshManager}
                messageHandler={messageHandler}
                messageStore={messageStore}
                panicMode={panicMode}
              />
            )}
          </Tab.Screen>
        </Tab.Navigator>
      </NavigationContainer>
      
      <Toast />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  tabBar: {
    backgroundColor: '#2a2a2a',
    borderTopColor: '#333',
    borderTopWidth: 1,
    height: 60,
    paddingBottom: 8,
    paddingTop: 8,
  },
  header: {
    backgroundColor: '#2a2a2a',
    borderBottomColor: '#333',
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  tabIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
});

export default App;
