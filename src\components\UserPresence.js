/**
 * UserPresence - Display connected users and their status
 * Shows online users in the mesh network
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';

const UserPresence = ({devices = [], onUserPress}) => {
  const getDeviceIcon = (device) => {
    // Simple device type detection based on name or other properties
    const name = device.name?.toLowerCase() || '';
    
    if (name.includes('iphone') || name.includes('ios')) {
      return '📱';
    } else if (name.includes('android')) {
      return '📱';
    } else if (name.includes('laptop') || name.includes('macbook')) {
      return '💻';
    } else if (name.includes('desktop') || name.includes('pc')) {
      return '🖥️';
    } else if (name.includes('tablet') || name.includes('ipad')) {
      return '📱';
    }
    
    return '📡'; // Default mesh device icon
  };

  const getSignalStrength = (rssi) => {
    if (!rssi) return '📶';
    
    if (rssi > -50) return '📶'; // Excellent
    if (rssi > -60) return '📶'; // Good
    if (rssi > -70) return '📶'; // Fair
    return '📶'; // Weak
  };

  const formatDeviceName = (device) => {
    if (device.name && device.name !== 'Unknown') {
      return device.name.length > 12 
        ? device.name.substring(0, 12) + '...'
        : device.name;
    }
    
    // Use first 8 characters of device ID
    return device.id ? device.id.substring(0, 8) + '...' : 'Unknown';
  };

  const getConnectionStatus = (device) => {
    const now = Date.now();
    const lastSeen = device.lastSeen || device.timestamp || now;
    const timeDiff = now - lastSeen;
    
    if (timeDiff < 30000) { // Less than 30 seconds
      return 'online';
    } else if (timeDiff < 300000) { // Less than 5 minutes
      return 'away';
    } else {
      return 'offline';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online':
        return '#00ff88';
      case 'away':
        return '#ffaa00';
      case 'offline':
        return '#666';
      default:
        return '#666';
    }
  };

  if (!devices || devices.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.emptyText}>No devices connected</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {devices.map((device, index) => {
          const status = getConnectionStatus(device);
          const statusColor = getStatusColor(status);
          
          return (
            <TouchableOpacity
              key={device.id || index}
              style={styles.deviceContainer}
              onPress={() => onUserPress && onUserPress(device)}
              activeOpacity={0.7}
            >
              <View style={styles.deviceInfo}>
                <View style={styles.deviceHeader}>
                  <Text style={styles.deviceIcon}>
                    {getDeviceIcon(device)}
                  </Text>
                  <View style={[
                    styles.statusIndicator,
                    {backgroundColor: statusColor}
                  ]} />
                </View>
                
                <Text style={styles.deviceName}>
                  {formatDeviceName(device)}
                </Text>
                
                <View style={styles.deviceStats}>
                  <Text style={styles.signalIcon}>
                    {getSignalStrength(device.rssi)}
                  </Text>
                  {device.rssi && (
                    <Text style={styles.rssiText}>
                      {device.rssi}dBm
                    </Text>
                  )}
                </View>
                
                {device.hopCount && device.hopCount > 1 && (
                  <Text style={styles.hopCount}>
                    {device.hopCount} hops
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
      
      <View style={styles.summary}>
        <Text style={styles.summaryText}>
          {devices.length} device{devices.length !== 1 ? 's' : ''}
        </Text>
        <View style={styles.statusSummary}>
          <View style={styles.statusItem}>
            <View style={[styles.statusDot, {backgroundColor: '#00ff88'}]} />
            <Text style={styles.statusCount}>
              {devices.filter(d => getConnectionStatus(d) === 'online').length}
            </Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusDot, {backgroundColor: '#ffaa00'}]} />
            <Text style={styles.statusCount}>
              {devices.filter(d => getConnectionStatus(d) === 'away').length}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#2a2a2a',
  },
  emptyText: {
    color: '#666',
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 8,
  },
  scrollContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  deviceContainer: {
    marginHorizontal: 4,
    backgroundColor: '#333',
    borderRadius: 8,
    padding: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  deviceInfo: {
    alignItems: 'center',
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  deviceIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 2,
  },
  deviceName: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  deviceStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  signalIcon: {
    fontSize: 10,
    marginRight: 2,
  },
  rssiText: {
    color: '#666',
    fontSize: 8,
  },
  hopCount: {
    color: '#ffaa00',
    fontSize: 8,
    fontStyle: 'italic',
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderTopWidth: 1,
    borderTopColor: '#444',
  },
  summaryText: {
    color: '#666',
    fontSize: 10,
  },
  statusSummary: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  statusCount: {
    color: '#666',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default UserPresence;
