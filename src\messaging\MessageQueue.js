/**
 * MessageQueue - Offline message queuing and management
 * Handles message queuing, retry logic, and offline storage
 */

import {EventEmitter} from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class MessageQueue extends EventEmitter {
  constructor() {
    super();
    this.queue = new Map(); // messageId -> queueItem
    this.retryQueue = new Map(); // messageId -> retryInfo
    this.storageKey = 'meshtalk_message_queue';
    this.retryStorageKey = 'meshtalk_retry_queue';
    this.isInitialized = false;
    this.processingInterval = null;
  }

  /**
   * Initialize message queue
   */
  async initialize() {
    try {
      Logger.info('MessageQueue: Initializing...');
      
      // Load persisted queue
      await this.loadQueue();
      await this.loadRetryQueue();
      
      // Start processing
      this.startProcessing();
      
      this.isInitialized = true;
      Logger.info(`MessageQueue: Initialized with ${this.queue.size} queued messages`);
    } catch (error) {
      Logger.error('MessageQueue: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Add message to queue
   */
  async enqueue(message, priority = 'normal') {
    try {
      if (!this.isInitialized) {
        throw new Error('MessageQueue not initialized');
      }

      const queueItem = {
        id: message.id,
        message,
        priority,
        queuedAt: Date.now(),
        attempts: 0,
        lastAttempt: null,
        status: 'pending',
        targetDeviceId: message.targetDeviceId,
        roomId: message.roomId,
      };

      this.queue.set(message.id, queueItem);
      
      // Persist queue
      await this.persistQueue();
      
      this.emit('messageQueued', queueItem);
      Logger.debug(`MessageQueue: Queued message ${message.id} with priority ${priority}`);
      
      return queueItem;
    } catch (error) {
      Logger.error(`MessageQueue: Failed to enqueue message ${message.id}`, error);
      throw error;
    }
  }

  /**
   * Remove message from queue
   */
  async remove(messageId) {
    try {
      const queueItem = this.queue.get(messageId);
      if (queueItem) {
        this.queue.delete(messageId);
        this.retryQueue.delete(messageId);
        
        await this.persistQueue();
        await this.persistRetryQueue();
        
        this.emit('messageRemoved', queueItem);
        Logger.debug(`MessageQueue: Removed message ${messageId} from queue`);
        
        return queueItem;
      }
      
      return null;
    } catch (error) {
      Logger.error(`MessageQueue: Failed to remove message ${messageId}`, error);
      throw error;
    }
  }

  /**
   * Get ready messages for processing
   */
  async getReadyMessages(limit = APP_CONFIG.MESSAGING.BATCH_SIZE) {
    try {
      const readyMessages = [];
      const now = Date.now();
      
      // Sort by priority and queue time
      const sortedItems = Array.from(this.queue.values()).sort((a, b) => {
        // Priority order: high, normal, low
        const priorityOrder = {high: 0, normal: 1, low: 2};
        const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
        
        if (priorityDiff !== 0) {
          return priorityDiff;
        }
        
        // Then by queue time (older first)
        return a.queuedAt - b.queuedAt;
      });
      
      for (const queueItem of sortedItems) {
        if (readyMessages.length >= limit) break;
        
        // Check if message is ready for processing
        if (this.isMessageReady(queueItem, now)) {
          readyMessages.push(queueItem.message);
          
          // Update attempt info
          queueItem.attempts++;
          queueItem.lastAttempt = now;
          queueItem.status = 'processing';
        }
      }
      
      if (readyMessages.length > 0) {
        await this.persistQueue();
      }
      
      return readyMessages;
    } catch (error) {
      Logger.error('MessageQueue: Failed to get ready messages', error);
      return [];
    }
  }

  /**
   * Check if message is ready for processing
   */
  isMessageReady(queueItem, now = Date.now()) {
    // Check if message is pending
    if (queueItem.status !== 'pending') {
      return false;
    }
    
    // Check if message has expired
    const age = now - queueItem.queuedAt;
    if (age > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
      this.markExpired(queueItem);
      return false;
    }
    
    // Check retry delay
    const retryInfo = this.retryQueue.get(queueItem.id);
    if (retryInfo) {
      const timeSinceLastAttempt = now - retryInfo.lastAttempt;
      const retryDelay = this.calculateRetryDelay(retryInfo.attempts);
      
      if (timeSinceLastAttempt < retryDelay) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Mark message as successfully sent
   */
  async markSent(messageId) {
    try {
      const queueItem = this.queue.get(messageId);
      if (queueItem) {
        queueItem.status = 'sent';
        queueItem.sentAt = Date.now();
        
        // Remove from retry queue
        this.retryQueue.delete(messageId);
        
        await this.persistQueue();
        await this.persistRetryQueue();
        
        this.emit('messageSent', queueItem);
        Logger.debug(`MessageQueue: Marked message ${messageId} as sent`);
      }
    } catch (error) {
      Logger.error(`MessageQueue: Failed to mark message ${messageId} as sent`, error);
    }
  }

  /**
   * Mark message as failed and schedule retry
   */
  async markFailed(messageId, error) {
    try {
      const queueItem = this.queue.get(messageId);
      if (!queueItem) return;
      
      queueItem.status = 'pending';
      queueItem.lastError = error.message;
      
      // Add to retry queue
      const retryInfo = this.retryQueue.get(messageId) || {
        messageId,
        attempts: 0,
        firstAttempt: Date.now(),
      };
      
      retryInfo.attempts++;
      retryInfo.lastAttempt = Date.now();
      retryInfo.lastError = error.message;
      
      // Check if max retries exceeded
      if (retryInfo.attempts >= APP_CONFIG.MESSAGING.MAX_RETRIES) {
        queueItem.status = 'failed';
        this.retryQueue.delete(messageId);
        this.emit('messageFailed', queueItem);
        Logger.warn(`MessageQueue: Message ${messageId} failed after ${retryInfo.attempts} attempts`);
      } else {
        this.retryQueue.set(messageId, retryInfo);
        this.emit('messageRetryScheduled', {queueItem, retryInfo});
        Logger.debug(`MessageQueue: Scheduled retry ${retryInfo.attempts} for message ${messageId}`);
      }
      
      await this.persistQueue();
      await this.persistRetryQueue();
    } catch (error) {
      Logger.error(`MessageQueue: Failed to mark message ${messageId} as failed`, error);
    }
  }

  /**
   * Mark message as expired
   */
  markExpired(queueItem) {
    queueItem.status = 'expired';
    queueItem.expiredAt = Date.now();
    
    this.retryQueue.delete(queueItem.id);
    this.emit('messageExpired', queueItem);
    
    Logger.debug(`MessageQueue: Message ${queueItem.id} expired`);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempts) {
    const baseDelay = 1000; // 1 second
    const maxDelay = 60000; // 1 minute
    const delay = Math.min(baseDelay * Math.pow(2, attempts - 1), maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return delay + jitter;
  }

  /**
   * Get messages by target device
   */
  getMessagesByDevice(deviceId) {
    return Array.from(this.queue.values())
      .filter(item => item.targetDeviceId === deviceId)
      .map(item => item.message);
  }

  /**
   * Get messages by room
   */
  getMessagesByRoom(roomId) {
    return Array.from(this.queue.values())
      .filter(item => item.roomId === roomId)
      .map(item => item.message);
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    const stats = {
      total: this.queue.size,
      pending: 0,
      processing: 0,
      sent: 0,
      failed: 0,
      expired: 0,
      retrying: this.retryQueue.size,
    };
    
    for (const item of this.queue.values()) {
      stats[item.status]++;
    }
    
    return stats;
  }

  /**
   * Get queue size
   */
  getQueueSize() {
    return this.queue.size;
  }

  /**
   * Clear all messages from queue
   */
  async clear() {
    try {
      this.queue.clear();
      this.retryQueue.clear();
      
      await this.persistQueue();
      await this.persistRetryQueue();
      
      this.emit('queueCleared');
      Logger.info('MessageQueue: Queue cleared');
    } catch (error) {
      Logger.error('MessageQueue: Failed to clear queue', error);
    }
  }

  /**
   * Load queue from storage
   */
  async loadQueue() {
    try {
      const queueData = await AsyncStorage.getItem(this.storageKey);
      if (queueData) {
        const items = JSON.parse(queueData);
        for (const item of items) {
          this.queue.set(item.id, item);
        }
        Logger.debug(`MessageQueue: Loaded ${items.length} messages from storage`);
      }
    } catch (error) {
      Logger.error('MessageQueue: Failed to load queue from storage', error);
    }
  }

  /**
   * Load retry queue from storage
   */
  async loadRetryQueue() {
    try {
      const retryData = await AsyncStorage.getItem(this.retryStorageKey);
      if (retryData) {
        const items = JSON.parse(retryData);
        for (const item of items) {
          this.retryQueue.set(item.messageId, item);
        }
        Logger.debug(`MessageQueue: Loaded ${items.length} retry items from storage`);
      }
    } catch (error) {
      Logger.error('MessageQueue: Failed to load retry queue from storage', error);
    }
  }

  /**
   * Persist queue to storage
   */
  async persistQueue() {
    try {
      const items = Array.from(this.queue.values());
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(items));
    } catch (error) {
      Logger.error('MessageQueue: Failed to persist queue to storage', error);
    }
  }

  /**
   * Persist retry queue to storage
   */
  async persistRetryQueue() {
    try {
      const items = Array.from(this.retryQueue.values());
      await AsyncStorage.setItem(this.retryStorageKey, JSON.stringify(items));
    } catch (error) {
      Logger.error('MessageQueue: Failed to persist retry queue to storage', error);
    }
  }

  /**
   * Start processing interval
   */
  startProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 5000); // Process every 5 seconds
  }

  /**
   * Process queue for ready messages
   */
  async processQueue() {
    try {
      const readyMessages = await this.getReadyMessages();
      
      for (const message of readyMessages) {
        this.emit('messageReady', message);
      }
    } catch (error) {
      Logger.error('MessageQueue: Queue processing failed', error);
    }
  }

  /**
   * Cleanup expired messages
   */
  async cleanup() {
    try {
      Logger.info('MessageQueue: Starting cleanup...');
      
      const now = Date.now();
      const expiredIds = [];
      
      // Find expired messages
      for (const [messageId, queueItem] of this.queue) {
        const age = now - queueItem.queuedAt;
        if (age > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
          expiredIds.push(messageId);
        }
      }
      
      // Remove expired messages
      for (const messageId of expiredIds) {
        await this.remove(messageId);
      }
      
      Logger.info(`MessageQueue: Cleanup completed, removed ${expiredIds.length} expired messages`);
    } catch (error) {
      Logger.error('MessageQueue: Cleanup failed', error);
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('MessageQueue: Emergency cleanup initiated');
      
      await this.clear();
      await AsyncStorage.removeItem(this.storageKey);
      await AsyncStorage.removeItem(this.retryStorageKey);
      
      Logger.warn('MessageQueue: Emergency cleanup completed');
    } catch (error) {
      Logger.error('MessageQueue: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown message queue
   */
  async shutdown() {
    try {
      Logger.info('MessageQueue: Shutting down...');
      
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
        this.processingInterval = null;
      }
      
      await this.persistQueue();
      await this.persistRetryQueue();
      
      this.removeAllListeners();
      
      Logger.info('MessageQueue: Shutdown complete');
    } catch (error) {
      Logger.error('MessageQueue: Shutdown failed', error);
    }
  }
}

export default MessageQueue;
