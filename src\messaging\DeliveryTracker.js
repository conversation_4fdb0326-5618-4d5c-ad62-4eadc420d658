/**
 * DeliveryTracker - Message delivery status tracking
 * Tracks message delivery confirmations and timeouts
 */

import {EventEmitter} from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class DeliveryTracker extends EventEmitter {
  constructor() {
    super();
    this.trackedMessages = new Map(); // messageId -> deliveryInfo
    this.deliveryTimeouts = new Map(); // messageId -> timeoutId
    this.storageKey = 'meshtalk_delivery_tracker';
    this.isInitialized = false;
  }

  /**
   * Initialize delivery tracker
   */
  async initialize() {
    try {
      Logger.info('DeliveryTracker: Initializing...');
      
      // Load persisted tracking data
      await this.loadTrackingData();
      
      // Setup cleanup interval
      this.startCleanupInterval();
      
      this.isInitialized = true;
      Logger.info(`DeliveryTracker: Initialized with ${this.trackedMessages.size} tracked messages`);
    } catch (error) {
      Logger.error('DeliveryTracker: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Start tracking a message
   */
  trackMessage(messageId, targetDeviceId = null, expectedRecipients = []) {
    try {
      if (!this.isInitialized) {
        throw new Error('DeliveryTracker not initialized');
      }

      const deliveryInfo = {
        messageId,
        targetDeviceId,
        expectedRecipients: expectedRecipients.length > 0 ? expectedRecipients : (targetDeviceId ? [targetDeviceId] : []),
        deliveredTo: new Set(),
        failedTo: new Set(),
        startTime: Date.now(),
        status: 'pending',
        attempts: 0,
        lastAttempt: Date.now(),
      };

      this.trackedMessages.set(messageId, deliveryInfo);
      
      // Set delivery timeout
      this.setDeliveryTimeout(messageId);
      
      // Persist tracking data
      this.persistTrackingData();
      
      this.emit('trackingStarted', deliveryInfo);
      Logger.debug(`DeliveryTracker: Started tracking message ${messageId}`);
      
      return deliveryInfo;
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to track message ${messageId}`, error);
      throw error;
    }
  }

  /**
   * Mark message as delivered to a specific device
   */
  markDelivered(messageId, deviceId) {
    try {
      const deliveryInfo = this.trackedMessages.get(messageId);
      if (!deliveryInfo) {
        Logger.warn(`DeliveryTracker: No tracking info found for message ${messageId}`);
        return false;
      }

      // Add to delivered set
      deliveryInfo.deliveredTo.add(deviceId);
      
      // Remove from failed set if present
      deliveryInfo.failedTo.delete(deviceId);
      
      // Update status
      this.updateDeliveryStatus(deliveryInfo);
      
      // Persist changes
      this.persistTrackingData();
      
      this.emit('messageDelivered', {messageId, deviceId, deliveryInfo});
      Logger.debug(`DeliveryTracker: Message ${messageId} delivered to ${deviceId}`);
      
      // Check if fully delivered
      if (this.isFullyDelivered(messageId)) {
        this.completeTracking(messageId);
      }
      
      return true;
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to mark message ${messageId} as delivered`, error);
      return false;
    }
  }

  /**
   * Mark message as failed to a specific device
   */
  markFailed(messageId, deviceId, error = null) {
    try {
      const deliveryInfo = this.trackedMessages.get(messageId);
      if (!deliveryInfo) {
        Logger.warn(`DeliveryTracker: No tracking info found for message ${messageId}`);
        return false;
      }

      // Add to failed set
      deliveryInfo.failedTo.add(deviceId);
      deliveryInfo.lastError = error ? error.message : 'Delivery failed';
      deliveryInfo.attempts++;
      
      // Update status
      this.updateDeliveryStatus(deliveryInfo);
      
      // Persist changes
      this.persistTrackingData();
      
      this.emit('messageDeliveryFailed', {messageId, deviceId, error, deliveryInfo});
      Logger.debug(`DeliveryTracker: Message ${messageId} failed to deliver to ${deviceId}`);
      
      return true;
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to mark message ${messageId} as failed`, error);
      return false;
    }
  }

  /**
   * Check if message is fully delivered
   */
  isFullyDelivered(messageId) {
    const deliveryInfo = this.trackedMessages.get(messageId);
    if (!deliveryInfo) return false;
    
    // If no expected recipients, consider delivered if any delivery occurred
    if (deliveryInfo.expectedRecipients.length === 0) {
      return deliveryInfo.deliveredTo.size > 0;
    }
    
    // Check if all expected recipients received the message
    for (const recipient of deliveryInfo.expectedRecipients) {
      if (!deliveryInfo.deliveredTo.has(recipient)) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Check if message delivery has completely failed
   */
  isCompletelyFailed(messageId) {
    const deliveryInfo = this.trackedMessages.get(messageId);
    if (!deliveryInfo) return false;
    
    // If no expected recipients, consider failed if no deliveries and some failures
    if (deliveryInfo.expectedRecipients.length === 0) {
      return deliveryInfo.deliveredTo.size === 0 && deliveryInfo.failedTo.size > 0;
    }
    
    // Check if all expected recipients failed
    for (const recipient of deliveryInfo.expectedRecipients) {
      if (!deliveryInfo.failedTo.has(recipient)) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Update delivery status based on current state
   */
  updateDeliveryStatus(deliveryInfo) {
    if (this.isFullyDelivered(deliveryInfo.messageId)) {
      deliveryInfo.status = 'delivered';
      deliveryInfo.completedAt = Date.now();
    } else if (this.isCompletelyFailed(deliveryInfo.messageId)) {
      deliveryInfo.status = 'failed';
      deliveryInfo.completedAt = Date.now();
    } else if (deliveryInfo.deliveredTo.size > 0) {
      deliveryInfo.status = 'partial';
    } else {
      deliveryInfo.status = 'pending';
    }
  }

  /**
   * Set delivery timeout for a message
   */
  setDeliveryTimeout(messageId) {
    // Clear existing timeout
    this.clearDeliveryTimeout(messageId);
    
    const timeoutId = setTimeout(() => {
      this.handleDeliveryTimeout(messageId);
    }, APP_CONFIG.MESSAGING.DELIVERY_TIMEOUT);
    
    this.deliveryTimeouts.set(messageId, timeoutId);
  }

  /**
   * Clear delivery timeout for a message
   */
  clearDeliveryTimeout(messageId) {
    const timeoutId = this.deliveryTimeouts.get(messageId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.deliveryTimeouts.delete(messageId);
    }
  }

  /**
   * Handle delivery timeout
   */
  handleDeliveryTimeout(messageId) {
    try {
      const deliveryInfo = this.trackedMessages.get(messageId);
      if (!deliveryInfo) return;
      
      // Mark as timed out if not delivered
      if (deliveryInfo.status === 'pending') {
        deliveryInfo.status = 'timeout';
        deliveryInfo.completedAt = Date.now();
        
        // Mark all expected recipients as failed
        for (const recipient of deliveryInfo.expectedRecipients) {
          if (!deliveryInfo.deliveredTo.has(recipient)) {
            deliveryInfo.failedTo.add(recipient);
          }
        }
        
        this.persistTrackingData();
        
        this.emit('deliveryTimeout', {messageId, deliveryInfo});
        Logger.warn(`DeliveryTracker: Message ${messageId} delivery timed out`);
      }
      
      // Clean up timeout
      this.deliveryTimeouts.delete(messageId);
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to handle delivery timeout for ${messageId}`, error);
    }
  }

  /**
   * Complete tracking for a message
   */
  completeTracking(messageId) {
    try {
      const deliveryInfo = this.trackedMessages.get(messageId);
      if (!deliveryInfo) return;
      
      // Clear timeout
      this.clearDeliveryTimeout(messageId);
      
      // Update completion time
      deliveryInfo.completedAt = Date.now();
      
      this.emit('trackingCompleted', deliveryInfo);
      Logger.debug(`DeliveryTracker: Completed tracking for message ${messageId}`);
      
      // Keep tracking info for a while for statistics
      setTimeout(() => {
        this.removeTracking(messageId);
      }, 60000); // Remove after 1 minute
      
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to complete tracking for ${messageId}`, error);
    }
  }

  /**
   * Remove tracking for a message
   */
  removeTracking(messageId) {
    try {
      const deliveryInfo = this.trackedMessages.get(messageId);
      if (deliveryInfo) {
        this.trackedMessages.delete(messageId);
        this.clearDeliveryTimeout(messageId);
        
        this.persistTrackingData();
        
        this.emit('trackingRemoved', deliveryInfo);
        Logger.debug(`DeliveryTracker: Removed tracking for message ${messageId}`);
      }
    } catch (error) {
      Logger.error(`DeliveryTracker: Failed to remove tracking for ${messageId}`, error);
    }
  }

  /**
   * Get delivery info for a message
   */
  getDeliveryInfo(messageId) {
    return this.trackedMessages.get(messageId);
  }

  /**
   * Get all tracked messages
   */
  getAllTrackedMessages() {
    return Array.from(this.trackedMessages.values());
  }

  /**
   * Get delivery statistics
   */
  getDeliveryStats() {
    const stats = {
      total: this.trackedMessages.size,
      pending: 0,
      partial: 0,
      delivered: 0,
      failed: 0,
      timeout: 0,
      averageDeliveryTime: 0,
    };
    
    let totalDeliveryTime = 0;
    let deliveredCount = 0;
    
    for (const deliveryInfo of this.trackedMessages.values()) {
      stats[deliveryInfo.status]++;
      
      if (deliveryInfo.status === 'delivered' && deliveryInfo.completedAt) {
        totalDeliveryTime += deliveryInfo.completedAt - deliveryInfo.startTime;
        deliveredCount++;
      }
    }
    
    if (deliveredCount > 0) {
      stats.averageDeliveryTime = totalDeliveryTime / deliveredCount;
    }
    
    return stats;
  }

  /**
   * Get tracked message count
   */
  getTrackedCount() {
    return this.trackedMessages.size;
  }

  /**
   * Load tracking data from storage
   */
  async loadTrackingData() {
    try {
      const trackingData = await AsyncStorage.getItem(this.storageKey);
      if (trackingData) {
        const items = JSON.parse(trackingData);
        for (const item of items) {
          // Convert delivered and failed arrays back to Sets
          item.deliveredTo = new Set(item.deliveredTo || []);
          item.failedTo = new Set(item.failedTo || []);
          
          this.trackedMessages.set(item.messageId, item);
          
          // Restore timeouts for pending messages
          if (item.status === 'pending') {
            this.setDeliveryTimeout(item.messageId);
          }
        }
        Logger.debug(`DeliveryTracker: Loaded ${items.length} tracking items from storage`);
      }
    } catch (error) {
      Logger.error('DeliveryTracker: Failed to load tracking data from storage', error);
    }
  }

  /**
   * Persist tracking data to storage
   */
  async persistTrackingData() {
    try {
      const items = Array.from(this.trackedMessages.values()).map(item => ({
        ...item,
        // Convert Sets to arrays for JSON serialization
        deliveredTo: Array.from(item.deliveredTo),
        failedTo: Array.from(item.failedTo),
      }));
      
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(items));
    } catch (error) {
      Logger.error('DeliveryTracker: Failed to persist tracking data to storage', error);
    }
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    setInterval(() => {
      this.cleanup();
    }, APP_CONFIG.STORAGE.CLEANUP_INTERVAL);
  }

  /**
   * Cleanup old tracking data
   */
  async cleanup() {
    try {
      Logger.debug('DeliveryTracker: Starting cleanup...');
      
      const now = Date.now();
      const expiredIds = [];
      
      // Find expired tracking entries
      for (const [messageId, deliveryInfo] of this.trackedMessages) {
        const age = now - deliveryInfo.startTime;
        const isCompleted = deliveryInfo.completedAt && (now - deliveryInfo.completedAt > 300000); // 5 minutes after completion
        
        if (age > APP_CONFIG.MESSAGING.MESSAGE_TTL || isCompleted) {
          expiredIds.push(messageId);
        }
      }
      
      // Remove expired entries
      for (const messageId of expiredIds) {
        this.removeTracking(messageId);
      }
      
      if (expiredIds.length > 0) {
        Logger.debug(`DeliveryTracker: Cleanup completed, removed ${expiredIds.length} expired entries`);
      }
    } catch (error) {
      Logger.error('DeliveryTracker: Cleanup failed', error);
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('DeliveryTracker: Emergency cleanup initiated');
      
      // Clear all timeouts
      for (const timeoutId of this.deliveryTimeouts.values()) {
        clearTimeout(timeoutId);
      }
      
      // Clear all data
      this.trackedMessages.clear();
      this.deliveryTimeouts.clear();
      
      // Remove from storage
      await AsyncStorage.removeItem(this.storageKey);
      
      Logger.warn('DeliveryTracker: Emergency cleanup completed');
    } catch (error) {
      Logger.error('DeliveryTracker: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown delivery tracker
   */
  async shutdown() {
    try {
      Logger.info('DeliveryTracker: Shutting down...');
      
      // Clear all timeouts
      for (const timeoutId of this.deliveryTimeouts.values()) {
        clearTimeout(timeoutId);
      }
      this.deliveryTimeouts.clear();
      
      // Persist final state
      await this.persistTrackingData();
      
      this.removeAllListeners();
      
      Logger.info('DeliveryTracker: Shutdown complete');
    } catch (error) {
      Logger.error('DeliveryTracker: Shutdown failed', error);
    }
  }
}

export default DeliveryTracker;
