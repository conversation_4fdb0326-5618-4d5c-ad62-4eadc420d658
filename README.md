# 🌐 MeshTalk - Decentralized Bluetooth Mesh Messaging

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React Native](https://img.shields.io/badge/React%20Native-0.72-blue.svg)](https://reactnative.dev/)
[![Bluetooth](https://img.shields.io/badge/Bluetooth-LE%205.0-blue.svg)](https://www.bluetooth.com/)
[![Platform](https://img.shields.io/badge/Platform-iOS%20%7C%20Android-lightgrey.svg)](https://reactnative.dev/)

> **Zero-infrastructure messaging that works anywhere, anytime**

MeshTalk is a revolutionary decentralized messaging application that operates exclusively through Bluetooth Low Energy mesh networks. No internet, no servers, no registration - just pure peer-to-peer communication that can't be censored, tracked, or shut down.

## 🚀 Alternative Product Names (Available Domains)

- **MeshWhisper** - Silent communication through the mesh
- **CryptoMesh** - Encrypted mesh networking made simple  
- **ShadowNet** - Anonymous messaging in the shadows
- **GhostChat** - Invisible communication network
- **SilentMesh** - Speak without being heard
- **VoidTalk** - Communication from the void
- **DarkMesh** - Secure messaging in dark networks
- **PhantomChat** - Ghostly peer-to-peer messaging
- **StealthNet** - Invisible network communication
- **EchoMesh** - Messages that echo through the mesh

## 🎯 Problem Statement

In an era of increasing digital surveillance, internet censorship, and infrastructure failures, people need a communication method that:
- Works without internet or cellular networks
- Cannot be monitored or censored by governments
- Operates during natural disasters and emergencies
- Protects user privacy and anonymity
- Costs nothing to use

## ✨ Key Features

### 🔒 **Privacy First**
- **Zero Registration**: No accounts, emails, or phone numbers
- **Anonymous Identity**: Cryptographically generated device IDs
- **Local Storage Only**: No cloud servers or data collection
- **Panic Mode**: Triple-tap to instantly delete all data
- **Forward Secrecy**: Messages can't be decrypted even if keys are compromised

### 🌐 **Mesh Networking**
- **Bluetooth LE Mesh**: 100-300m range between devices
- **Auto-Discovery**: Automatic device detection and pairing
- **Multi-Hop Relay**: Messages route through intermediate devices
- **Self-Healing**: Network adapts when devices leave
- **50+ Concurrent Users**: Supports large mesh networks

### 💬 **Messaging**
- **Hashtag Rooms**: Join conversations with #hashtags
- **Private Channels**: Password-protected group chats
- **Ephemeral Messages**: Auto-deletion after delivery
- **Offline Queuing**: Messages stored until delivery possible
- **Real-time Delivery**: <2 second message latency

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Device A"
        A1[UI Layer]
        A2[Message Handler]
        A3[Encryption Engine]
        A4[BLE Mesh Manager]
        A5[Local Storage]
    end
    
    subgraph "Device B"
        B1[UI Layer]
        B2[Message Handler]
        B3[Encryption Engine]
        B4[BLE Mesh Manager]
        B5[Local Storage]
    end
    
    subgraph "Device C"
        C1[UI Layer]
        C2[Message Handler]
        C3[Encryption Engine]
        C4[BLE Mesh Manager]
        C5[Local Storage]
    end
    
    A4 <--> B4
    B4 <--> C4
    A4 <--> C4
    
    A1 --> A2 --> A3 --> A4
    A4 --> A3 --> A2 --> A1
    A2 <--> A5
    
    B1 --> B2 --> B3 --> B4
    B4 --> B3 --> B2 --> B1
    B2 <--> B5
    
    C1 --> C2 --> C3 --> C4
    C4 --> C3 --> C2 --> C1
    C2 <--> C5
```

## 🔄 Message Flow Workflow

```mermaid
sequenceDiagram
    participant A as Device A
    participant B as Device B (Relay)
    participant C as Device C
    
    Note over A,C: User A sends message to User C
    
    A->>A: Encrypt message with C's public key
    A->>A: Sign with A's private key
    A->>B: Broadcast encrypted message
    
    Note over B: Device B acts as relay
    B->>B: Verify signature
    B->>B: Check if destination in range
    B->>C: Forward message to Device C
    
    C->>C: Verify A's signature
    C->>C: Decrypt with C's private key
    C->>C: Display message
    C->>B: Send delivery confirmation
    B->>A: Forward confirmation
    
    Note over A,C: Message auto-deletes after confirmation
    A->>A: Delete message from storage
    C->>C: Delete message after TTL

## 📁 Project Structure

```
MeshTalk/
├── 📱 src/
│   ├── 🔗 bluetooth/
│   │   ├── MeshManager.js          # Core BLE mesh networking
│   │   ├── DeviceDiscovery.js      # Device scanning & pairing
│   │   ├── ConnectionManager.js    # Connection state management
│   │   └── MessageRouter.js        # Multi-hop message routing
│   ├── 🔐 crypto/
│   │   ├── EncryptionEngine.js     # End-to-end encryption
│   │   ├── KeyManager.js           # Key generation & exchange
│   │   ├── SignalProtocol.js       # Signal protocol implementation
│   │   └── CryptoUtils.js          # Cryptographic utilities
│   ├── 💬 messaging/
│   │   ├── MessageHandler.js       # Message processing
│   │   ├── MessageQueue.js         # Offline message queuing
│   │   ├── RoomManager.js          # Hashtag room management
│   │   └── DeliveryTracker.js      # Message delivery status
│   ├── 💾 storage/
│   │   ├── LocalStorage.js         # Encrypted local storage
│   │   ├── MessageStore.js         # Ephemeral message storage
│   │   ├── UserStore.js            # User preferences
│   │   └── PanicMode.js            # Emergency data deletion
│   ├── 🎨 components/
│   │   ├── ChatScreen.js           # Main chat interface
│   │   ├── RoomList.js             # Available rooms display
│   │   ├── MessageBubble.js        # Individual message component
│   │   ├── UserPresence.js         # Online users indicator
│   │   ├── NetworkTopology.js      # Mesh network visualization
│   │   └── SettingsScreen.js       # App configuration
│   ├── 🛠️ utils/
│   │   ├── DeviceUtils.js          # Device information utilities
│   │   ├── NetworkUtils.js         # Network topology helpers
│   │   ├── ValidationUtils.js      # Input validation
│   │   └── PerformanceUtils.js     # Battery & performance optimization
│   └── 📋 constants/
│       ├── Config.js               # App configuration constants
│       ├── BluetoothConstants.js   # BLE specific constants
│       └── CryptoConstants.js      # Encryption parameters
├── 🧪 __tests__/
│   ├── bluetooth/                  # Bluetooth functionality tests
│   ├── crypto/                     # Encryption tests
│   ├── messaging/                  # Message handling tests
│   └── components/                 # UI component tests
├── 📱 android/                     # Android-specific code
├── 🍎 ios/                         # iOS-specific code
├── 📦 package.json                 # Dependencies & scripts
├── 🔧 metro.config.js              # Metro bundler configuration
├── 🎯 babel.config.js              # Babel transpiler config
└── 📖 README.md                    # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm/yarn
- React Native CLI
- Android Studio (for Android)
- Xcode (for iOS)
- Physical devices with Bluetooth LE support

### Installation

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/MeshTalk.git
cd MeshTalk

# Install dependencies
npm install

# iOS setup
cd ios && pod install && cd ..

# Run on Android
npx react-native run-android

# Run on iOS
npx react-native run-ios
```

## 🔧 Technical Specifications

| Feature | Specification |
|---------|---------------|
| **Range** | 100-300 meters between devices |
| **Concurrent Users** | 50+ devices in mesh network |
| **Message Size** | 1KB maximum per message |
| **Latency** | <2 seconds delivery time |
| **Battery Life** | Optimized for 24+ hours background |
| **Encryption** | AES-256 + Signal Protocol |
| **Platform** | iOS 12+, Android 8+ |
| **Bluetooth** | BLE 5.0+ required |

## 🛡️ Security Features

- **Zero Knowledge Architecture**: No servers can access your data
- **Perfect Forward Secrecy**: Past messages stay secure even if keys are compromised
- **Anonymous Identities**: Cryptographically generated device IDs
- **Secure Key Exchange**: ECDH key agreement protocol
- **Message Authentication**: Digital signatures prevent tampering
- **Panic Mode**: Triple-tap home button to delete all data
- **Auto-Deletion**: Messages automatically expire
- **No Metadata**: No logs, analytics, or tracking

## 🌍 Use Cases

### 🚨 **Emergency Communication**
- Natural disasters when cell towers are down
- Emergency responders coordination
- Disaster relief operations
- Remote area communication

### 🗳️ **Activism & Journalism**
- Protest organization without surveillance
- Journalist source protection
- Whistleblower communications
- Bypassing internet censorship

### 🎪 **Events & Gatherings**
- Festival coordination
- Conference networking
- Large event messaging
- Temporary community networks

### 🏔️ **Remote Areas**
- Hiking and camping groups
- Rural community networks
- Maritime communication
- Underground facilities

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Signal Protocol for encryption inspiration
- Bluetooth SIG for BLE specifications
- React Native community for mobile framework
- Open source cryptography libraries

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [MeshTalk Community](https://discord.gg/meshtalk)
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/MeshTalk/issues)
- 📖 Docs: [Documentation](https://docs.meshtalk.app)

---

**⚠️ Disclaimer**: This software is provided for educational and research purposes. Users are responsible for compliance with local laws and regulations regarding radio frequency usage and encryption.
```
