/**
 * MessageBubble - Individual message display component
 * Renders messages with different styles for sent/received
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

const MessageBubble = ({message, isOwnMessage, onPress, onLongPress}) => {
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
  };

  const formatSender = (senderId) => {
    if (!senderId) return 'Unknown';
    // Show first 8 characters of sender ID
    return senderId.substring(0, 8) + '...';
  };

  const getMessageTypeIcon = (type) => {
    switch (type) {
      case 'text':
        return '';
      case 'encrypted':
        return '🔒 ';
      case 'system':
        return '⚙️ ';
      default:
        return '';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isOwnMessage ? styles.ownMessage : styles.otherMessage,
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      <View style={[
        styles.bubble,
        isOwnMessage ? styles.ownBubble : styles.otherBubble,
      ]}>
        {!isOwnMessage && (
          <Text style={styles.senderName}>
            {formatSender(message.senderId)}
          </Text>
        )}
        
        <View style={styles.messageContent}>
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
          ]}>
            {getMessageTypeIcon(message.type)}{message.content}
          </Text>
        </View>
        
        <View style={styles.messageFooter}>
          <Text style={[
            styles.timestamp,
            isOwnMessage ? styles.ownTimestamp : styles.otherTimestamp,
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          
          {isOwnMessage && (
            <View style={styles.statusContainer}>
              {message.delivered && (
                <Text style={styles.deliveryStatus}>✓</Text>
              )}
              {message.read && (
                <Text style={styles.readStatus}>✓✓</Text>
              )}
            </View>
          )}
        </View>
        
        {message.roomId && (
          <Text style={styles.roomTag}>
            {message.roomId}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    marginHorizontal: 8,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    borderRadius: 16,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  ownBubble: {
    backgroundColor: '#00ff88',
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    backgroundColor: '#333',
    borderBottomLeftRadius: 4,
  },
  senderName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#00ff88',
    marginBottom: 4,
  },
  messageContent: {
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#000',
  },
  otherMessageText: {
    color: '#fff',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  timestamp: {
    fontSize: 11,
    opacity: 0.7,
  },
  ownTimestamp: {
    color: '#000',
  },
  otherTimestamp: {
    color: '#fff',
  },
  statusContainer: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  deliveryStatus: {
    fontSize: 12,
    color: '#000',
    opacity: 0.7,
  },
  readStatus: {
    fontSize: 12,
    color: '#000',
    opacity: 0.7,
    marginLeft: 2,
  },
  roomTag: {
    fontSize: 10,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
});

export default MessageBubble;
