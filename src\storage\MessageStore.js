/**
 * MessageStore - Ephemeral message storage
 * Handles temporary message storage with automatic deletion
 */

import {EventEmitter} from 'events';
import LocalStorage from './LocalStorage';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class MessageStore extends EventEmitter {
  constructor() {
    super();
    this.localStorage = new LocalStorage();
    this.messageCache = new Map(); // In-memory cache for recent messages
    this.messageIndex = new Map(); // messageId -> metadata
    this.roomMessages = new Map(); // roomId -> messageIds[]
    this.userMessages = new Map(); // userId -> messageIds[]
    this.isInitialized = false;
    this.cleanupInterval = null;
  }

  /**
   * Initialize message store
   */
  async initialize() {
    try {
      Logger.info('MessageStore: Initializing...');
      
      await this.localStorage.initialize();
      await this.loadMessageIndex();
      
      // Start cleanup interval
      this.startCleanupInterval();
      
      this.isInitialized = true;
      Logger.info(`MessageStore: Initialized with ${this.messageIndex.size} messages`);
    } catch (error) {
      Logger.error('MessageStore: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Store a message
   */
  async storeMessage(message, ttl = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('MessageStore not initialized');
      }

      const messageId = message.id;
      const messageTTL = ttl || APP_CONFIG.MESSAGING.MESSAGE_TTL;
      
      // Create message metadata
      const metadata = {
        id: messageId,
        type: message.type,
        senderId: message.senderId,
        targetDeviceId: message.targetDeviceId,
        roomId: message.roomId,
        timestamp: message.timestamp,
        storedAt: Date.now(),
        ttl: messageTTL,
        size: JSON.stringify(message).length,
      };

      // Store message in encrypted storage
      await this.localStorage.setItem(`message_${messageId}`, message, messageTTL);
      
      // Update index
      this.messageIndex.set(messageId, metadata);
      
      // Update room index
      if (message.roomId) {
        this.addToRoomIndex(message.roomId, messageId);
      }
      
      // Update user index
      if (message.senderId) {
        this.addToUserIndex(message.senderId, messageId);
      }
      
      // Cache recent message
      this.cacheMessage(message);
      
      // Persist index
      await this.persistMessageIndex();
      
      this.emit('messageStored', {messageId, metadata});
      Logger.debug(`MessageStore: Stored message ${messageId}`);
      
      return metadata;
    } catch (error) {
      Logger.error(`MessageStore: Failed to store message ${message.id}`, error);
      throw error;
    }
  }

  /**
   * Retrieve a message
   */
  async getMessage(messageId) {
    try {
      if (!this.isInitialized) {
        throw new Error('MessageStore not initialized');
      }

      // Check cache first
      if (this.messageCache.has(messageId)) {
        const cachedMessage = this.messageCache.get(messageId);
        Logger.debug(`MessageStore: Retrieved message ${messageId} from cache`);
        return cachedMessage.message;
      }

      // Check if message exists in index
      const metadata = this.messageIndex.get(messageId);
      if (!metadata) {
        return null;
      }

      // Retrieve from storage
      const message = await this.localStorage.getItem(`message_${messageId}`);
      if (!message) {
        // Message expired or corrupted, remove from index
        await this.removeMessageFromIndex(messageId);
        return null;
      }

      // Cache the message
      this.cacheMessage(message);
      
      Logger.debug(`MessageStore: Retrieved message ${messageId} from storage`);
      return message;
    } catch (error) {
      Logger.error(`MessageStore: Failed to retrieve message ${messageId}`, error);
      return null;
    }
  }

  /**
   * Remove a message
   */
  async removeMessage(messageId) {
    try {
      const metadata = this.messageIndex.get(messageId);
      if (!metadata) {
        return false;
      }

      // Remove from storage
      await this.localStorage.removeItem(`message_${messageId}`);
      
      // Remove from index
      await this.removeMessageFromIndex(messageId);
      
      // Remove from cache
      this.messageCache.delete(messageId);
      
      this.emit('messageRemoved', {messageId, metadata});
      Logger.debug(`MessageStore: Removed message ${messageId}`);
      
      return true;
    } catch (error) {
      Logger.error(`MessageStore: Failed to remove message ${messageId}`, error);
      return false;
    }
  }

  /**
   * Get messages by room
   */
  async getMessagesByRoom(roomId, limit = 50, offset = 0) {
    try {
      const messageIds = this.roomMessages.get(roomId) || [];
      const selectedIds = messageIds.slice(offset, offset + limit);
      
      const messages = [];
      for (const messageId of selectedIds) {
        const message = await this.getMessage(messageId);
        if (message) {
          messages.push(message);
        }
      }
      
      // Sort by timestamp
      messages.sort((a, b) => a.timestamp - b.timestamp);
      
      Logger.debug(`MessageStore: Retrieved ${messages.length} messages for room ${roomId}`);
      return messages;
    } catch (error) {
      Logger.error(`MessageStore: Failed to get messages for room ${roomId}`, error);
      return [];
    }
  }

  /**
   * Get messages by user
   */
  async getMessagesByUser(userId, limit = 50, offset = 0) {
    try {
      const messageIds = this.userMessages.get(userId) || [];
      const selectedIds = messageIds.slice(offset, offset + limit);
      
      const messages = [];
      for (const messageId of selectedIds) {
        const message = await this.getMessage(messageId);
        if (message) {
          messages.push(message);
        }
      }
      
      // Sort by timestamp
      messages.sort((a, b) => a.timestamp - b.timestamp);
      
      Logger.debug(`MessageStore: Retrieved ${messages.length} messages for user ${userId}`);
      return messages;
    } catch (error) {
      Logger.error(`MessageStore: Failed to get messages for user ${userId}`, error);
      return [];
    }
  }

  /**
   * Get recent messages
   */
  async getRecentMessages(limit = 20) {
    try {
      // Get recent messages from cache first
      const cachedMessages = Array.from(this.messageCache.values())
        .sort((a, b) => b.cachedAt - a.cachedAt)
        .slice(0, limit)
        .map(item => item.message);
      
      if (cachedMessages.length >= limit) {
        return cachedMessages;
      }
      
      // Get additional messages from index
      const allMetadata = Array.from(this.messageIndex.values())
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, limit);
      
      const messages = [];
      for (const metadata of allMetadata) {
        const message = await this.getMessage(metadata.id);
        if (message) {
          messages.push(message);
        }
      }
      
      Logger.debug(`MessageStore: Retrieved ${messages.length} recent messages`);
      return messages;
    } catch (error) {
      Logger.error('MessageStore: Failed to get recent messages', error);
      return [];
    }
  }

  /**
   * Search messages
   */
  async searchMessages(query, roomId = null, userId = null, limit = 50) {
    try {
      const searchResults = [];
      const queryLower = query.toLowerCase();
      
      // Filter metadata based on criteria
      let candidateMetadata = Array.from(this.messageIndex.values());
      
      if (roomId) {
        candidateMetadata = candidateMetadata.filter(m => m.roomId === roomId);
      }
      
      if (userId) {
        candidateMetadata = candidateMetadata.filter(m => m.senderId === userId);
      }
      
      // Search through messages
      for (const metadata of candidateMetadata) {
        if (searchResults.length >= limit) break;
        
        const message = await this.getMessage(metadata.id);
        if (message && message.content && 
            message.content.toLowerCase().includes(queryLower)) {
          searchResults.push(message);
        }
      }
      
      // Sort by relevance (timestamp for now)
      searchResults.sort((a, b) => b.timestamp - a.timestamp);
      
      Logger.debug(`MessageStore: Search found ${searchResults.length} messages for query "${query}"`);
      return searchResults;
    } catch (error) {
      Logger.error(`MessageStore: Search failed for query "${query}"`, error);
      return [];
    }
  }

  /**
   * Cache message in memory
   */
  cacheMessage(message) {
    this.messageCache.set(message.id, {
      message,
      cachedAt: Date.now(),
    });
    
    // Limit cache size
    if (this.messageCache.size > APP_CONFIG.UI.MAX_VISIBLE_MESSAGES) {
      const oldestKey = Array.from(this.messageCache.entries())
        .sort((a, b) => a[1].cachedAt - b[1].cachedAt)[0][0];
      this.messageCache.delete(oldestKey);
    }
  }

  /**
   * Add message to room index
   */
  addToRoomIndex(roomId, messageId) {
    if (!this.roomMessages.has(roomId)) {
      this.roomMessages.set(roomId, []);
    }
    
    const roomMessageIds = this.roomMessages.get(roomId);
    if (!roomMessageIds.includes(messageId)) {
      roomMessageIds.push(messageId);
      
      // Limit room message history
      const maxMessages = APP_CONFIG.STORAGE.MAX_MESSAGES;
      if (roomMessageIds.length > maxMessages) {
        const removedId = roomMessageIds.shift();
        this.removeMessage(removedId); // Remove old message
      }
    }
  }

  /**
   * Add message to user index
   */
  addToUserIndex(userId, messageId) {
    if (!this.userMessages.has(userId)) {
      this.userMessages.set(userId, []);
    }
    
    const userMessageIds = this.userMessages.get(userId);
    if (!userMessageIds.includes(messageId)) {
      userMessageIds.push(messageId);
      
      // Limit user message history
      const maxMessages = APP_CONFIG.STORAGE.MAX_MESSAGES;
      if (userMessageIds.length > maxMessages) {
        userMessageIds.shift(); // Remove oldest reference
      }
    }
  }

  /**
   * Remove message from all indexes
   */
  async removeMessageFromIndex(messageId) {
    const metadata = this.messageIndex.get(messageId);
    if (!metadata) return;
    
    // Remove from main index
    this.messageIndex.delete(messageId);
    
    // Remove from room index
    if (metadata.roomId) {
      const roomMessageIds = this.roomMessages.get(metadata.roomId);
      if (roomMessageIds) {
        const index = roomMessageIds.indexOf(messageId);
        if (index > -1) {
          roomMessageIds.splice(index, 1);
        }
      }
    }
    
    // Remove from user index
    if (metadata.senderId) {
      const userMessageIds = this.userMessages.get(metadata.senderId);
      if (userMessageIds) {
        const index = userMessageIds.indexOf(messageId);
        if (index > -1) {
          userMessageIds.splice(index, 1);
        }
      }
    }
    
    // Persist changes
    await this.persistMessageIndex();
  }

  /**
   * Load message index from storage
   */
  async loadMessageIndex() {
    try {
      const indexData = await this.localStorage.getItem('message_index');
      if (indexData) {
        // Restore message index
        for (const metadata of indexData.messages || []) {
          this.messageIndex.set(metadata.id, metadata);
        }
        
        // Restore room index
        for (const [roomId, messageIds] of Object.entries(indexData.roomMessages || {})) {
          this.roomMessages.set(roomId, messageIds);
        }
        
        // Restore user index
        for (const [userId, messageIds] of Object.entries(indexData.userMessages || {})) {
          this.userMessages.set(userId, messageIds);
        }
        
        Logger.debug(`MessageStore: Loaded index with ${this.messageIndex.size} messages`);
      }
    } catch (error) {
      Logger.error('MessageStore: Failed to load message index', error);
    }
  }

  /**
   * Persist message index to storage
   */
  async persistMessageIndex() {
    try {
      const indexData = {
        messages: Array.from(this.messageIndex.values()),
        roomMessages: Object.fromEntries(this.roomMessages),
        userMessages: Object.fromEntries(this.userMessages),
        lastUpdated: Date.now(),
      };
      
      await this.localStorage.setItem('message_index', indexData);
    } catch (error) {
      Logger.error('MessageStore: Failed to persist message index', error);
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    try {
      const localStorageStats = await this.localStorage.getStorageStats();
      
      return {
        totalMessages: this.messageIndex.size,
        cachedMessages: this.messageCache.size,
        roomCount: this.roomMessages.size,
        userCount: this.userMessages.size,
        totalSize: localStorageStats.totalSize,
        averageMessageSize: this.messageIndex.size > 0 
          ? Array.from(this.messageIndex.values()).reduce((sum, m) => sum + m.size, 0) / this.messageIndex.size
          : 0,
      };
    } catch (error) {
      Logger.error('MessageStore: Failed to get storage stats', error);
      return {
        totalMessages: 0,
        cachedMessages: 0,
        roomCount: 0,
        userCount: 0,
        totalSize: 0,
        averageMessageSize: 0,
      };
    }
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, APP_CONFIG.STORAGE.CLEANUP_INTERVAL);
  }

  /**
   * Cleanup expired messages
   */
  async cleanup() {
    try {
      Logger.debug('MessageStore: Starting cleanup...');
      
      const now = Date.now();
      const expiredIds = [];
      
      // Find expired messages
      for (const [messageId, metadata] of this.messageIndex) {
        const age = now - metadata.storedAt;
        if (age > metadata.ttl) {
          expiredIds.push(messageId);
        }
      }
      
      // Remove expired messages
      for (const messageId of expiredIds) {
        await this.removeMessage(messageId);
      }
      
      // Cleanup cache
      for (const [messageId, cacheItem] of this.messageCache) {
        const age = now - cacheItem.cachedAt;
        if (age > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
          this.messageCache.delete(messageId);
        }
      }
      
      if (expiredIds.length > 0) {
        Logger.debug(`MessageStore: Cleanup completed, removed ${expiredIds.length} expired messages`);
      }
    } catch (error) {
      Logger.error('MessageStore: Cleanup failed', error);
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('MessageStore: Emergency cleanup initiated');
      
      // Clear all in-memory data
      this.messageCache.clear();
      this.messageIndex.clear();
      this.roomMessages.clear();
      this.userMessages.clear();
      
      // Emergency cleanup of local storage
      await this.localStorage.emergencyCleanup();
      
      Logger.warn('MessageStore: Emergency cleanup completed');
    } catch (error) {
      Logger.error('MessageStore: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown message store
   */
  async shutdown() {
    try {
      Logger.info('MessageStore: Shutting down...');
      
      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
      }
      
      // Persist final state
      await this.persistMessageIndex();
      
      // Shutdown local storage
      await this.localStorage.shutdown();
      
      this.removeAllListeners();
      
      Logger.info('MessageStore: Shutdown complete');
    } catch (error) {
      Logger.error('MessageStore: Shutdown failed', error);
    }
  }
}

export default MessageStore;
