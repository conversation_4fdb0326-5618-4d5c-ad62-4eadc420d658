/**
 * MessageRouter - Multi-hop message routing for mesh network
 * Handles message forwarding, routing decisions, and delivery tracking
 */

import {EventEmitter} from 'events';
import {BLE_CHARACTERISTICS, MESSAGE_TYPES} from '@constants/BluetoothConstants';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class MessageRouter extends EventEmitter {
  constructor() {
    super();
    this.routingTable = new Map(); // destination -> nextHop
    this.messageCache = new Map(); // messageId -> messageInfo
    this.pendingMessages = new Map(); // messageId -> retryInfo
    this.forwardingHistory = new Map(); // messageId -> forwardedDevices
    this.deliveryCallbacks = new Map(); // messageId -> callback
    
    this.cleanupInterval = null;
  }

  /**
   * Initialize message router
   */
  async initialize() {
    try {
      Logger.info('MessageRouter: Initializing...');
      
      // Start cleanup interval
      this.startCleanupInterval();
      
      Logger.info('MessageRouter: Initialized successfully');
    } catch (error) {
      Logger.error('MessageRouter: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Route a message through the mesh network
   */
  async routeMessage(message, route, connectionManager) {
    try {
      const messageId = this.generateMessageId();
      const routedMessage = {
        id: messageId,
        ...message,
        route: route.slice(), // Copy route
        hopCount: 0,
        maxHops: APP_CONFIG.MESH.MAX_HOPS,
        timestamp: Date.now(),
        ttl: APP_CONFIG.MESSAGING.MESSAGE_TTL,
      };

      Logger.info(`MessageRouter: Routing message ${messageId} through ${route.length} hops`);
      
      // Cache message
      this.cacheMessage(routedMessage);
      
      // Send to first hop or broadcast
      if (route.length === 1 && route[0] === 'broadcast') {
        return await this.broadcastMessage(routedMessage, connectionManager);
      } else {
        return await this.sendToNextHop(routedMessage, connectionManager);
      }
    } catch (error) {
      Logger.error('MessageRouter: Failed to route message', error);
      throw error;
    }
  }

  /**
   * Send message to next hop
   */
  async sendToNextHop(message, connectionManager) {
    const nextHop = message.route[message.hopCount];
    if (!nextHop) {
      throw new Error('No next hop available');
    }

    try {
      // Check if we've reached the destination
      if (message.hopCount >= message.route.length - 1) {
        return await this.deliverMessage(message, nextHop, connectionManager);
      }

      // Forward to next hop
      return await this.forwardMessage(message, nextHop, connectionManager);
    } catch (error) {
      Logger.error(`MessageRouter: Failed to send to next hop ${nextHop}`, error);
      
      // Try alternative route if available
      return await this.tryAlternativeRoute(message, connectionManager);
    }
  }

  /**
   * Forward message to intermediate hop
   */
  async forwardMessage(message, deviceId, connectionManager) {
    try {
      // Check if already forwarded to this device
      const forwardedDevices = this.forwardingHistory.get(message.id) || new Set();
      if (forwardedDevices.has(deviceId)) {
        Logger.warn(`MessageRouter: Message ${message.id} already forwarded to ${deviceId}`);
        return false;
      }

      // Increment hop count
      const forwardedMessage = {
        ...message,
        hopCount: message.hopCount + 1,
        forwardedBy: connectionManager.getDeviceId(),
        forwardedAt: Date.now(),
      };

      // Check hop limit
      if (forwardedMessage.hopCount >= forwardedMessage.maxHops) {
        Logger.warn(`MessageRouter: Message ${message.id} exceeded max hops`);
        this.emit('messageDropped', {message, reason: 'max_hops_exceeded'});
        return false;
      }

      // Send message
      const success = await this.sendMessageToDevice(forwardedMessage, deviceId, connectionManager);
      
      if (success) {
        // Track forwarding
        forwardedDevices.add(deviceId);
        this.forwardingHistory.set(message.id, forwardedDevices);
        
        this.emit('messageForwarded', {message: forwardedMessage, deviceId});
        Logger.info(`MessageRouter: Forwarded message ${message.id} to ${deviceId}`);
      }

      return success;
    } catch (error) {
      Logger.error(`MessageRouter: Failed to forward message to ${deviceId}`, error);
      return false;
    }
  }

  /**
   * Deliver message to final destination
   */
  async deliverMessage(message, deviceId, connectionManager) {
    try {
      const success = await this.sendMessageToDevice(message, deviceId, connectionManager);
      
      if (success) {
        this.emit('messageDelivered', {message, deviceId});
        Logger.info(`MessageRouter: Delivered message ${message.id} to ${deviceId}`);
        
        // Execute delivery callback if exists
        const callback = this.deliveryCallbacks.get(message.id);
        if (callback) {
          callback(null, {message, deviceId});
          this.deliveryCallbacks.delete(message.id);
        }
      } else {
        // Retry delivery
        this.scheduleRetry(message, deviceId, connectionManager);
      }

      return success;
    } catch (error) {
      Logger.error(`MessageRouter: Failed to deliver message to ${deviceId}`, error);
      
      // Execute error callback
      const callback = this.deliveryCallbacks.get(message.id);
      if (callback) {
        callback(error, null);
        this.deliveryCallbacks.delete(message.id);
      }
      
      return false;
    }
  }

  /**
   * Broadcast message to all connected devices
   */
  async broadcastMessage(message, connectionManager) {
    const connectedDevices = connectionManager.getConnectedDeviceIds();
    const results = [];

    Logger.info(`MessageRouter: Broadcasting message ${message.id} to ${connectedDevices.length} devices`);

    for (const deviceId of connectedDevices) {
      try {
        const success = await this.sendMessageToDevice(message, deviceId, connectionManager);
        results.push({deviceId, success});
        
        if (success) {
          this.emit('messageBroadcast', {message, deviceId});
        }
      } catch (error) {
        Logger.error(`MessageRouter: Failed to broadcast to ${deviceId}`, error);
        results.push({deviceId, success: false, error});
      }
    }

    return results;
  }

  /**
   * Send message to specific device
   */
  async sendMessageToDevice(message, deviceId, connectionManager) {
    try {
      // Check if device is connected
      if (!connectionManager.isConnected(deviceId)) {
        Logger.warn(`MessageRouter: Device ${deviceId} not connected`);
        return false;
      }

      // Serialize message
      const messageData = this.serializeMessage(message);
      
      // Send via MESSAGE_TX characteristic
      await connectionManager.writeCharacteristic(
        deviceId,
        BLE_CHARACTERISTICS.MESSAGE_TX,
        messageData
      );

      Logger.debug(`MessageRouter: Sent message ${message.id} to ${deviceId}`);
      return true;
    } catch (error) {
      Logger.error(`MessageRouter: Failed to send message to ${deviceId}`, error);
      return false;
    }
  }

  /**
   * Handle received message
   */
  async handleReceivedMessage(messageData, fromDeviceId, connectionManager) {
    try {
      const message = this.deserializeMessage(messageData);
      
      // Check if message is duplicate
      if (this.messageCache.has(message.id)) {
        Logger.debug(`MessageRouter: Duplicate message ${message.id} from ${fromDeviceId}`);
        return;
      }

      // Check TTL
      if (Date.now() - message.timestamp > message.ttl) {
        Logger.warn(`MessageRouter: Message ${message.id} expired`);
        this.emit('messageExpired', {message, fromDeviceId});
        return;
      }

      // Cache message
      this.cacheMessage(message);

      // Send acknowledgment
      await this.sendAcknowledgment(message.id, fromDeviceId, connectionManager);

      // Check if message is for us
      const ourDeviceId = connectionManager.getDeviceId();
      if (message.targetDeviceId === ourDeviceId || !message.targetDeviceId) {
        this.emit('messageReceived', {message, fromDeviceId});
        Logger.info(`MessageRouter: Received message ${message.id} from ${fromDeviceId}`);
        return;
      }

      // Forward message if not for us
      if (message.hopCount < message.maxHops) {
        await this.forwardReceivedMessage(message, fromDeviceId, connectionManager);
      } else {
        Logger.warn(`MessageRouter: Message ${message.id} reached max hops, dropping`);
        this.emit('messageDropped', {message, reason: 'max_hops_exceeded'});
      }
    } catch (error) {
      Logger.error('MessageRouter: Failed to handle received message', error);
    }
  }

  /**
   * Forward received message to other devices
   */
  async forwardReceivedMessage(message, fromDeviceId, connectionManager) {
    try {
      // Find next hop for target
      const nextHop = this.findNextHop(message.targetDeviceId, fromDeviceId);
      
      if (nextHop) {
        await this.forwardMessage(message, nextHop, connectionManager);
      } else {
        // Broadcast if no specific route
        const connectedDevices = connectionManager.getConnectedDeviceIds()
          .filter(id => id !== fromDeviceId); // Don't send back to sender
        
        for (const deviceId of connectedDevices) {
          await this.forwardMessage(message, deviceId, connectionManager);
        }
      }
    } catch (error) {
      Logger.error('MessageRouter: Failed to forward received message', error);
    }
  }

  /**
   * Send acknowledgment
   */
  async sendAcknowledgment(messageId, deviceId, connectionManager) {
    try {
      const ackMessage = {
        type: MESSAGE_TYPES.ACK,
        messageId,
        timestamp: Date.now(),
        fromDeviceId: connectionManager.getDeviceId(),
      };

      const ackData = this.serializeMessage(ackMessage);
      
      await connectionManager.writeCharacteristic(
        deviceId,
        BLE_CHARACTERISTICS.MESSAGE_ACK,
        ackData
      );

      Logger.debug(`MessageRouter: Sent ACK for message ${messageId} to ${deviceId}`);
    } catch (error) {
      Logger.error(`MessageRouter: Failed to send ACK to ${deviceId}`, error);
    }
  }

  /**
   * Try alternative route
   */
  async tryAlternativeRoute(message, connectionManager) {
    // Simple implementation: try broadcasting
    Logger.info(`MessageRouter: Trying alternative route for message ${message.id}`);
    
    const broadcastMessage = {
      ...message,
      route: ['broadcast'],
      hopCount: 0,
    };

    return await this.broadcastMessage(broadcastMessage, connectionManager);
  }

  /**
   * Find next hop for destination
   */
  findNextHop(targetDeviceId, excludeDeviceId = null) {
    const nextHop = this.routingTable.get(targetDeviceId);
    
    if (nextHop && nextHop !== excludeDeviceId) {
      return nextHop;
    }
    
    return null;
  }

  /**
   * Update routing table
   */
  updateRoutingTable(routingTable) {
    this.routingTable.clear();
    
    for (const [destination, nextHop] of routingTable) {
      this.routingTable.set(destination, nextHop);
    }
    
    Logger.debug(`MessageRouter: Updated routing table with ${routingTable.size} entries`);
  }

  /**
   * Schedule message retry
   */
  scheduleRetry(message, deviceId, connectionManager) {
    const retryInfo = this.pendingMessages.get(message.id) || {
      message,
      deviceId,
      attempts: 0,
      lastAttempt: 0,
    };

    if (retryInfo.attempts >= APP_CONFIG.MESSAGING.MAX_RETRIES) {
      Logger.warn(`MessageRouter: Max retries exceeded for message ${message.id}`);
      this.pendingMessages.delete(message.id);
      this.emit('messageDeliveryFailed', {message, deviceId});
      return;
    }

    retryInfo.attempts++;
    retryInfo.lastAttempt = Date.now();
    this.pendingMessages.set(message.id, retryInfo);

    // Schedule retry
    setTimeout(() => {
      this.retryMessage(message.id, connectionManager);
    }, APP_CONFIG.MESSAGING.DELIVERY_TIMEOUT);
  }

  /**
   * Retry message delivery
   */
  async retryMessage(messageId, connectionManager) {
    const retryInfo = this.pendingMessages.get(messageId);
    if (!retryInfo) return;

    try {
      Logger.info(`MessageRouter: Retrying message ${messageId} (attempt ${retryInfo.attempts})`);
      
      const success = await this.sendMessageToDevice(
        retryInfo.message,
        retryInfo.deviceId,
        connectionManager
      );

      if (success) {
        this.pendingMessages.delete(messageId);
        this.emit('messageDelivered', {message: retryInfo.message, deviceId: retryInfo.deviceId});
      } else {
        this.scheduleRetry(retryInfo.message, retryInfo.deviceId, connectionManager);
      }
    } catch (error) {
      Logger.error(`MessageRouter: Retry failed for message ${messageId}`, error);
      this.scheduleRetry(retryInfo.message, retryInfo.deviceId, connectionManager);
    }
  }

  /**
   * Cache message
   */
  cacheMessage(message) {
    this.messageCache.set(message.id, {
      message,
      cachedAt: Date.now(),
    });

    // Limit cache size
    if (this.messageCache.size > APP_CONFIG.MESSAGING.MAX_QUEUE_SIZE) {
      const oldestKey = this.messageCache.keys().next().value;
      this.messageCache.delete(oldestKey);
    }
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Serialize message for transmission
   */
  serializeMessage(message) {
    try {
      const jsonString = JSON.stringify(message);
      return Buffer.from(jsonString, 'utf8');
    } catch (error) {
      Logger.error('MessageRouter: Failed to serialize message', error);
      throw error;
    }
  }

  /**
   * Deserialize received message
   */
  deserializeMessage(data) {
    try {
      const jsonString = data.toString('utf8');
      return JSON.parse(jsonString);
    } catch (error) {
      Logger.error('MessageRouter: Failed to deserialize message', error);
      throw error;
    }
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredMessages();
    }, APP_CONFIG.STORAGE.CLEANUP_INTERVAL);
  }

  /**
   * Cleanup expired messages
   */
  cleanupExpiredMessages() {
    const now = Date.now();
    
    // Cleanup message cache
    for (const [messageId, cacheInfo] of this.messageCache) {
      if (now - cacheInfo.cachedAt > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
        this.messageCache.delete(messageId);
      }
    }
    
    // Cleanup forwarding history
    for (const [messageId, devices] of this.forwardingHistory) {
      if (!this.messageCache.has(messageId)) {
        this.forwardingHistory.delete(messageId);
      }
    }
    
    // Cleanup pending messages
    for (const [messageId, retryInfo] of this.pendingMessages) {
      if (now - retryInfo.lastAttempt > APP_CONFIG.MESSAGING.DELIVERY_TIMEOUT * 2) {
        this.pendingMessages.delete(messageId);
        this.emit('messageDeliveryFailed', {
          message: retryInfo.message,
          deviceId: retryInfo.deviceId,
        });
      }
    }
  }

  /**
   * Set delivery callback
   */
  setDeliveryCallback(messageId, callback) {
    this.deliveryCallbacks.set(messageId, callback);
  }

  /**
   * Get routing statistics
   */
  getRoutingStats() {
    return {
      cachedMessages: this.messageCache.size,
      pendingMessages: this.pendingMessages.size,
      forwardingHistory: this.forwardingHistory.size,
      routingTableSize: this.routingTable.size,
    };
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    try {
      Logger.info('MessageRouter: Shutting down...');
      
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
      }
      
      this.messageCache.clear();
      this.pendingMessages.clear();
      this.forwardingHistory.clear();
      this.routingTable.clear();
      this.deliveryCallbacks.clear();
      
      this.removeAllListeners();
      
      Logger.info('MessageRouter: Shutdown complete');
    } catch (error) {
      Logger.error('MessageRouter: Shutdown failed', error);
    }
  }
}

export default MessageRouter;
