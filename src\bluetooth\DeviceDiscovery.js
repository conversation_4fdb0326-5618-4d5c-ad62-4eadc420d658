/**
 * DeviceDiscovery - Bluetooth device scanning and advertising
 * Handles device discovery, filtering, and advertising for mesh network
 */

import {EventEmitter} from 'events';
import {PermissionsAndroid, Platform} from 'react-native';
import {
  BLE_SERVICES,
  BLE_CONFIG,
  BLE_ERRORS,
  MESSAGE_TYPES,
} from '@constants/BluetoothConstants';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class DeviceDiscovery extends EventEmitter {
  constructor(bleManager) {
    super();
    this.bleManager = bleManager;
    this.discoveredDevices = new Map();
    this.scanSubscription = null;
    this.advertisingData = null;
    this.isScanning = false;
    this.isAdvertising = false;
    this.scanTimeout = null;
  }

  /**
   * Initialize device discovery
   */
  async initialize() {
    try {
      Logger.info('DeviceDiscovery: Initializing...');
      
      // Request permissions
      await this.requestPermissions();
      
      // Setup advertising data
      this.setupAdvertisingData();
      
      Logger.info('DeviceDiscovery: Initialized successfully');
    } catch (error) {
      Logger.error('DeviceDiscovery: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Request necessary permissions for BLE operations
   */
  async requestPermissions() {
    if (Platform.OS === 'android') {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADVERTISE,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
      ];

      const granted = await PermissionsAndroid.requestMultiple(permissions);
      
      for (const permission of permissions) {
        if (granted[permission] !== PermissionsAndroid.RESULTS.GRANTED) {
          throw new Error(`Permission ${permission} not granted`);
        }
      }
    }
  }

  /**
   * Setup advertising data for MeshTalk service
   */
  setupAdvertisingData() {
    this.advertisingData = {
      localName: 'MeshTalk',
      serviceUUIDs: [BLE_SERVICES.MESHTALK_SERVICE],
      manufacturerData: this.createManufacturerData(),
      txPowerLevel: BLE_CONFIG.ADVERTISING_TX_POWER,
      includeDeviceName: BLE_CONFIG.INCLUDE_DEVICE_NAME,
      includeTxPowerLevel: BLE_CONFIG.INCLUDE_TX_POWER,
    };
  }

  /**
   * Create manufacturer data for advertising
   */
  createManufacturerData() {
    const data = Buffer.alloc(8);
    data.writeUInt16LE(0xFFFF, 0); // Company ID (test/debug)
    data.writeUInt8(MESSAGE_TYPES.DISCOVERY, 2); // Message type
    data.writeUInt8(1, 3); // Protocol version
    data.writeUInt32LE(Date.now() & 0xFFFFFFFF, 4); // Timestamp
    return data.toString('base64');
  }

  /**
   * Start scanning for MeshTalk devices
   */
  async startScanning() {
    if (this.isScanning) {
      Logger.warn('DeviceDiscovery: Already scanning');
      return;
    }

    try {
      Logger.info('DeviceDiscovery: Starting scan...');
      
      // Clear previous discoveries
      this.discoveredDevices.clear();
      
      // Start scanning
      this.scanSubscription = this.bleManager.onDeviceDisconnected(
        (error, device) => {
          if (error) {
            Logger.error('DeviceDiscovery: Device disconnected with error', error);
          } else {
            this.handleDeviceDisconnected(device);
          }
        }
      );

      await this.bleManager.startDeviceScan(
        [BLE_SERVICES.MESHTALK_SERVICE],
        {
          allowDuplicates: BLE_CONFIG.ALLOW_DUPLICATES,
          scanMode: BLE_CONFIG.SCAN_MODE,
        },
        (error, device) => {
          if (error) {
            Logger.error('DeviceDiscovery: Scan error', error);
            this.emit('scanError', error);
          } else {
            this.handleDeviceDiscovered(device);
          }
        }
      );

      this.isScanning = true;
      
      // Set scan timeout
      this.scanTimeout = setTimeout(() => {
        this.stopScanning();
      }, BLE_CONFIG.SCAN_DURATION);

      this.emit('scanStarted');
      Logger.info('DeviceDiscovery: Scan started');
    } catch (error) {
      Logger.error('DeviceDiscovery: Failed to start scan', error);
      this.isScanning = false;
      throw error;
    }
  }

  /**
   * Stop scanning for devices
   */
  async stopScanning() {
    if (!this.isScanning) {
      return;
    }

    try {
      Logger.info('DeviceDiscovery: Stopping scan...');
      
      // Clear timeout
      if (this.scanTimeout) {
        clearTimeout(this.scanTimeout);
        this.scanTimeout = null;
      }
      
      // Stop scanning
      await this.bleManager.stopDeviceScan();
      
      // Cleanup subscription
      if (this.scanSubscription) {
        this.scanSubscription.remove();
        this.scanSubscription = null;
      }
      
      this.isScanning = false;
      this.emit('scanStopped');
      
      Logger.info('DeviceDiscovery: Scan stopped');
    } catch (error) {
      Logger.error('DeviceDiscovery: Failed to stop scan', error);
    }
  }

  /**
   * Start advertising MeshTalk service
   */
  async startAdvertising() {
    if (this.isAdvertising) {
      Logger.warn('DeviceDiscovery: Already advertising');
      return;
    }

    try {
      Logger.info('DeviceDiscovery: Starting advertising...');
      
      // Note: react-native-ble-plx doesn't support peripheral mode
      // This would need platform-specific implementation
      // For now, we'll simulate advertising through service setup
      
      await this.setupPeripheralServices();
      
      this.isAdvertising = true;
      this.emit('advertisingStarted');
      
      Logger.info('DeviceDiscovery: Advertising started');
    } catch (error) {
      Logger.error('DeviceDiscovery: Failed to start advertising', error);
      throw error;
    }
  }

  /**
   * Stop advertising
   */
  async stopAdvertising() {
    if (!this.isAdvertising) {
      return;
    }

    try {
      Logger.info('DeviceDiscovery: Stopping advertising...');
      
      // Stop advertising (platform-specific implementation needed)
      await this.teardownPeripheralServices();
      
      this.isAdvertising = false;
      this.emit('advertisingStopped');
      
      Logger.info('DeviceDiscovery: Advertising stopped');
    } catch (error) {
      Logger.error('DeviceDiscovery: Failed to stop advertising', error);
    }
  }

  /**
   * Setup peripheral services for advertising
   */
  async setupPeripheralServices() {
    // This would require platform-specific implementation
    // for BLE peripheral mode (GATT server)
    Logger.info('DeviceDiscovery: Setting up peripheral services');
    
    // TODO: Implement platform-specific peripheral mode
    // - iOS: Core Bluetooth peripheral manager
    // - Android: BluetoothGattServer
  }

  /**
   * Teardown peripheral services
   */
  async teardownPeripheralServices() {
    Logger.info('DeviceDiscovery: Tearing down peripheral services');
    
    // TODO: Implement platform-specific cleanup
  }

  /**
   * Handle discovered device
   */
  handleDeviceDiscovered(device) {
    try {
      // Filter MeshTalk devices
      if (!this.isMeshTalkDevice(device)) {
        return;
      }

      // Check signal strength
      if (device.rssi < APP_CONFIG.MESH.SIGNAL_STRENGTH_THRESHOLD) {
        Logger.debug(`DeviceDiscovery: Device ${device.id} signal too weak: ${device.rssi}dBm`);
        return;
      }

      // Update or add device
      const existingDevice = this.discoveredDevices.get(device.id);
      if (existingDevice) {
        // Update existing device info
        existingDevice.rssi = device.rssi;
        existingDevice.lastSeen = Date.now();
        this.emit('deviceUpdated', existingDevice);
      } else {
        // Add new device
        const deviceInfo = {
          id: device.id,
          name: device.name || device.localName || 'Unknown',
          rssi: device.rssi,
          manufacturerData: device.manufacturerData,
          serviceData: device.serviceData,
          serviceUUIDs: device.serviceUUIDs,
          discoveredAt: Date.now(),
          lastSeen: Date.now(),
        };
        
        this.discoveredDevices.set(device.id, deviceInfo);
        this.emit('deviceDiscovered', deviceInfo);
        
        Logger.info(`DeviceDiscovery: Discovered MeshTalk device ${device.id} (${device.name})`);
      }
    } catch (error) {
      Logger.error('DeviceDiscovery: Error handling discovered device', error);
    }
  }

  /**
   * Handle device disconnected
   */
  handleDeviceDisconnected(device) {
    if (this.discoveredDevices.has(device.id)) {
      const deviceInfo = this.discoveredDevices.get(device.id);
      this.discoveredDevices.delete(device.id);
      this.emit('deviceLost', deviceInfo);
      
      Logger.info(`DeviceDiscovery: Lost device ${device.id}`);
    }
  }

  /**
   * Check if device is a MeshTalk device
   */
  isMeshTalkDevice(device) {
    // Check service UUIDs
    if (device.serviceUUIDs && device.serviceUUIDs.includes(BLE_SERVICES.MESHTALK_SERVICE)) {
      return true;
    }
    
    // Check local name
    if (device.localName && device.localName.includes('MeshTalk')) {
      return true;
    }
    
    // Check manufacturer data
    if (device.manufacturerData) {
      try {
        const data = Buffer.from(device.manufacturerData, 'base64');
        if (data.length >= 3 && data.readUInt8(2) === MESSAGE_TYPES.DISCOVERY) {
          return true;
        }
      } catch (error) {
        // Ignore parsing errors
      }
    }
    
    return false;
  }

  /**
   * Get discovered devices
   */
  getDiscoveredDevices() {
    return Array.from(this.discoveredDevices.values());
  }

  /**
   * Get device by ID
   */
  getDevice(deviceId) {
    return this.discoveredDevices.get(deviceId);
  }

  /**
   * Clear discovered devices
   */
  clearDiscoveredDevices() {
    this.discoveredDevices.clear();
    this.emit('devicesCleared');
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    try {
      Logger.info('DeviceDiscovery: Shutting down...');
      
      await this.stopScanning();
      await this.stopAdvertising();
      
      this.clearDiscoveredDevices();
      this.removeAllListeners();
      
      Logger.info('DeviceDiscovery: Shutdown complete');
    } catch (error) {
      Logger.error('DeviceDiscovery: Shutdown failed', error);
    }
  }
}

export default DeviceDiscovery;
