/**
 * RoomList - Display and manage chat rooms
 * Shows available rooms and allows joining/creating new ones
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import {ROOM_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

const RoomList = ({meshManager, messageHandler}) => {
  const [rooms, setRooms] = useState([]);
  const [joinedRooms, setJoinedRooms] = useState(new Set());
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newRoomName, setNewRoomName] = useState('');
  const [newRoomPassword, setNewRoomPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    initializeRooms();
  }, []);

  /**
   * Initialize rooms list
   */
  const initializeRooms = async () => {
    try {
      setIsLoading(true);
      
      // Load default rooms
      const defaultRooms = ROOM_CONFIG.DEFAULT_ROOMS.map(hashtag => ({
        id: hashtag,
        name: hashtag.replace('#', ''),
        hashtag,
        description: `Default room for ${hashtag}`,
        isDefault: true,
        isProtected: false,
        participantCount: 0,
        messageCount: 0,
        lastActivity: Date.now(),
      }));
      
      setRooms(defaultRooms);
      
      // Auto-join general room
      setJoinedRooms(new Set(['#general']));
      
      setIsLoading(false);
    } catch (error) {
      Logger.error('RoomList: Initialization failed', error);
      setIsLoading(false);
    }
  };

  /**
   * Join a room
   */
  const joinRoom = async (room) => {
    try {
      if (joinedRooms.has(room.id)) {
        Alert.alert('Info', 'You are already in this room');
        return;
      }

      if (room.isProtected) {
        // Show password prompt
        Alert.prompt(
          'Room Password',
          `Enter password for ${room.hashtag}:`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Join',
              onPress: (password) => {
                if (password) {
                  performJoinRoom(room, password);
                }
              },
            },
          ],
          'secure-text'
        );
      } else {
        await performJoinRoom(room);
      }
    } catch (error) {
      Logger.error('RoomList: Failed to join room', error);
      Alert.alert('Error', 'Failed to join room');
    }
  };

  /**
   * Perform room join
   */
  const performJoinRoom = async (room, password = null) => {
    try {
      // In a real implementation, this would use RoomManager
      setJoinedRooms(prev => new Set([...prev, room.id]));
      
      Alert.alert('Success', `Joined ${room.hashtag}`);
      Logger.info(`RoomList: Joined room ${room.hashtag}`);
    } catch (error) {
      Logger.error('RoomList: Failed to perform room join', error);
      Alert.alert('Error', 'Failed to join room');
    }
  };

  /**
   * Leave a room
   */
  const leaveRoom = async (room) => {
    try {
      if (!joinedRooms.has(room.id)) {
        return;
      }

      Alert.alert(
        'Leave Room',
        `Are you sure you want to leave ${room.hashtag}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Leave',
            style: 'destructive',
            onPress: () => {
              setJoinedRooms(prev => {
                const newSet = new Set(prev);
                newSet.delete(room.id);
                return newSet;
              });
              
              Logger.info(`RoomList: Left room ${room.hashtag}`);
            },
          },
        ]
      );
    } catch (error) {
      Logger.error('RoomList: Failed to leave room', error);
    }
  };

  /**
   * Create new room
   */
  const createRoom = async () => {
    try {
      if (!newRoomName.trim()) {
        Alert.alert('Error', 'Please enter a room name');
        return;
      }

      const hashtag = newRoomName.startsWith('#') 
        ? newRoomName.toLowerCase()
        : `#${newRoomName.toLowerCase()}`;

      // Validate hashtag
      if (hashtag.length > ROOM_CONFIG.MAX_ROOM_NAME_LENGTH) {
        Alert.alert('Error', 'Room name is too long');
        return;
      }

      if (!/^#[a-zA-Z0-9_-]+$/.test(hashtag)) {
        Alert.alert('Error', 'Room name can only contain letters, numbers, underscore, and hyphen');
        return;
      }

      // Check if room already exists
      if (rooms.some(room => room.id === hashtag)) {
        Alert.alert('Error', 'Room already exists');
        return;
      }

      const newRoom = {
        id: hashtag,
        name: hashtag.replace('#', ''),
        hashtag,
        description: `Custom room: ${hashtag}`,
        isDefault: false,
        isProtected: !!newRoomPassword,
        participantCount: 1,
        messageCount: 0,
        lastActivity: Date.now(),
        createdBy: 'You',
      };

      setRooms(prev => [...prev, newRoom]);
      setJoinedRooms(prev => new Set([...prev, hashtag]));
      
      setShowCreateModal(false);
      setNewRoomName('');
      setNewRoomPassword('');
      
      Alert.alert('Success', `Created and joined ${hashtag}`);
      Logger.info(`RoomList: Created room ${hashtag}`);
    } catch (error) {
      Logger.error('RoomList: Failed to create room', error);
      Alert.alert('Error', 'Failed to create room');
    }
  };

  /**
   * Render room item
   */
  const renderRoom = ({item}) => {
    const isJoined = joinedRooms.has(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.roomItem,
          isJoined && styles.joinedRoomItem,
        ]}
        onPress={() => isJoined ? leaveRoom(item) : joinRoom(item)}
        activeOpacity={0.7}
      >
        <View style={styles.roomHeader}>
          <Text style={styles.roomHashtag}>{item.hashtag}</Text>
          {item.isProtected && (
            <Text style={styles.protectedIcon}>🔒</Text>
          )}
          {item.isDefault && (
            <Text style={styles.defaultBadge}>DEFAULT</Text>
          )}
        </View>
        
        <Text style={styles.roomDescription}>{item.description}</Text>
        
        <View style={styles.roomStats}>
          <Text style={styles.statText}>
            👥 {item.participantCount} participants
          </Text>
          <Text style={styles.statText}>
            💬 {item.messageCount} messages
          </Text>
        </View>
        
        <View style={styles.roomActions}>
          <Text style={[
            styles.actionText,
            isJoined ? styles.leaveText : styles.joinText,
          ]}>
            {isJoined ? 'Tap to Leave' : 'Tap to Join'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  /**
   * Render create room modal
   */
  const renderCreateModal = () => (
    <Modal
      visible={showCreateModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowCreateModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Create New Room</Text>
          
          <TextInput
            style={styles.modalInput}
            value={newRoomName}
            onChangeText={setNewRoomName}
            placeholder="Room name (e.g., #myroom)"
            placeholderTextColor="#666"
            autoCapitalize="none"
            maxLength={ROOM_CONFIG.MAX_ROOM_NAME_LENGTH}
          />
          
          <TextInput
            style={styles.modalInput}
            value={newRoomPassword}
            onChangeText={setNewRoomPassword}
            placeholder="Password (optional)"
            placeholderTextColor="#666"
            secureTextEntry
          />
          
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowCreateModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalButton, styles.createButton]}
              onPress={createRoom}
            >
              <Text style={styles.createButtonText}>Create</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Chat Rooms</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Text style={styles.createButtonText}>+ Create</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={rooms}
        renderItem={renderRoom}
        keyExtractor={(item) => item.id}
        style={styles.roomsList}
        contentContainerStyle={styles.roomsContainer}
        showsVerticalScrollIndicator={false}
      />
      
      {renderCreateModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  createButton: {
    backgroundColor: '#00ff88',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  createButtonText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
  },
  roomsList: {
    flex: 1,
  },
  roomsContainer: {
    padding: 16,
  },
  roomItem: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  joinedRoomItem: {
    borderColor: '#00ff88',
    backgroundColor: '#1a2a1a',
  },
  roomHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  roomHashtag: {
    color: '#00ff88',
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  protectedIcon: {
    fontSize: 16,
    marginLeft: 8,
  },
  defaultBadge: {
    backgroundColor: '#444',
    color: '#fff',
    fontSize: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  roomDescription: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 12,
  },
  roomStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statText: {
    color: '#666',
    fontSize: 12,
  },
  roomActions: {
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  joinText: {
    color: '#00ff88',
  },
  leaveText: {
    color: '#ff6b6b',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#2a2a2a',
    borderRadius: 16,
    padding: 24,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalInput: {
    backgroundColor: '#333',
    borderRadius: 8,
    padding: 12,
    color: '#fff',
    fontSize: 16,
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: '#444',
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default RoomList;
