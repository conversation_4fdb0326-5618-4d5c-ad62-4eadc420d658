/**
 * RoomManager - Hashtag-based room management
 * Handles room creation, joining, leaving, and message routing
 */

import {EventEmitter} from 'events';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CryptoJS from 'react-native-crypto-js';
import {v4 as uuidv4} from 'react-native-uuid';
import {ROOM_CONFIG, USER_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class RoomManager extends EventEmitter {
  constructor() {
    super();
    this.rooms = new Map(); // roomId -> roomInfo
    this.userRooms = new Set(); // rooms the user has joined
    this.roomParticipants = new Map(); // roomId -> Set of deviceIds
    this.roomMessages = new Map(); // roomId -> message history
    this.storageKey = 'meshtalk_rooms';
    this.userRoomsKey = 'meshtalk_user_rooms';
    this.isInitialized = false;
  }

  /**
   * Initialize room manager
   */
  async initialize() {
    try {
      Logger.info('RoomManager: Initializing...');
      
      // Load persisted room data
      await this.loadRooms();
      await this.loadUserRooms();
      
      // Create default rooms
      await this.createDefaultRooms();
      
      this.isInitialized = true;
      Logger.info(`RoomManager: Initialized with ${this.rooms.size} rooms`);
    } catch (error) {
      Logger.error('RoomManager: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Create or join a room by hashtag
   */
  async joinRoom(hashtag, password = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('RoomManager not initialized');
      }

      // Validate hashtag
      const roomId = this.validateAndNormalizeHashtag(hashtag);
      
      // Check if room exists
      let room = this.rooms.get(roomId);
      if (!room) {
        // Create new room
        room = await this.createRoom(roomId, password);
      } else {
        // Verify password if room is protected
        if (room.isProtected && !this.verifyRoomPassword(room, password)) {
          throw new Error('Invalid room password');
        }
      }
      
      // Add user to room
      this.userRooms.add(roomId);
      
      // Initialize participants set if not exists
      if (!this.roomParticipants.has(roomId)) {
        this.roomParticipants.set(roomId, new Set());
      }
      
      // Add user as participant
      const deviceId = await this.getCurrentDeviceId();
      this.roomParticipants.get(roomId).add(deviceId);
      
      // Update room info
      room.participantCount = this.roomParticipants.get(roomId).size;
      room.lastActivity = Date.now();
      
      // Persist changes
      await this.persistRooms();
      await this.persistUserRooms();
      
      this.emit('roomJoined', {room, deviceId});
      Logger.info(`RoomManager: Joined room ${roomId}`);
      
      return room;
    } catch (error) {
      Logger.error(`RoomManager: Failed to join room ${hashtag}`, error);
      throw error;
    }
  }

  /**
   * Leave a room
   */
  async leaveRoom(roomId) {
    try {
      const room = this.rooms.get(roomId);
      if (!room) {
        throw new Error(`Room ${roomId} not found`);
      }
      
      // Remove user from room
      this.userRooms.delete(roomId);
      
      // Remove from participants
      const deviceId = await this.getCurrentDeviceId();
      const participants = this.roomParticipants.get(roomId);
      if (participants) {
        participants.delete(deviceId);
        room.participantCount = participants.size;
      }
      
      // Clear message history for this room
      this.roomMessages.delete(roomId);
      
      // Persist changes
      await this.persistRooms();
      await this.persistUserRooms();
      
      this.emit('roomLeft', {room, deviceId});
      Logger.info(`RoomManager: Left room ${roomId}`);
      
      return room;
    } catch (error) {
      Logger.error(`RoomManager: Failed to leave room ${roomId}`, error);
      throw error;
    }
  }

  /**
   * Create a new room
   */
  async createRoom(roomId, password = null, description = null) {
    try {
      const room = {
        id: roomId,
        hashtag: roomId,
        name: roomId.replace('#', ''),
        description: description || `Room for ${roomId}`,
        isProtected: !!password,
        passwordHash: password ? this.hashPassword(password) : null,
        createdAt: Date.now(),
        createdBy: await this.getCurrentDeviceId(),
        lastActivity: Date.now(),
        participantCount: 0,
        messageCount: 0,
        isDefault: false,
      };
      
      this.rooms.set(roomId, room);
      this.roomParticipants.set(roomId, new Set());
      this.roomMessages.set(roomId, []);
      
      await this.persistRooms();
      
      this.emit('roomCreated', room);
      Logger.info(`RoomManager: Created room ${roomId}`);
      
      return room;
    } catch (error) {
      Logger.error(`RoomManager: Failed to create room ${roomId}`, error);
      throw error;
    }
  }

  /**
   * Add message to room
   */
  addMessageToRoom(roomId, message) {
    try {
      if (!this.rooms.has(roomId)) {
        Logger.warn(`RoomManager: Room ${roomId} not found for message`);
        return false;
      }
      
      // Get or create message history
      let messages = this.roomMessages.get(roomId);
      if (!messages) {
        messages = [];
        this.roomMessages.set(roomId, messages);
      }
      
      // Add message
      messages.push({
        ...message,
        roomId,
        addedAt: Date.now(),
      });
      
      // Limit message history
      const maxMessages = 100; // Keep last 100 messages per room
      if (messages.length > maxMessages) {
        messages.splice(0, messages.length - maxMessages);
      }
      
      // Update room stats
      const room = this.rooms.get(roomId);
      room.messageCount++;
      room.lastActivity = Date.now();
      
      this.emit('messageAddedToRoom', {roomId, message});
      Logger.debug(`RoomManager: Added message to room ${roomId}`);
      
      return true;
    } catch (error) {
      Logger.error(`RoomManager: Failed to add message to room ${roomId}`, error);
      return false;
    }
  }

  /**
   * Get room messages
   */
  getRoomMessages(roomId, limit = 50) {
    const messages = this.roomMessages.get(roomId) || [];
    return messages.slice(-limit); // Return last N messages
  }

  /**
   * Get room participants
   */
  getRoomParticipants(roomId) {
    const participants = this.roomParticipants.get(roomId);
    return participants ? Array.from(participants) : [];
  }

  /**
   * Add participant to room
   */
  addParticipantToRoom(roomId, deviceId) {
    try {
      if (!this.rooms.has(roomId)) {
        Logger.warn(`RoomManager: Room ${roomId} not found`);
        return false;
      }
      
      let participants = this.roomParticipants.get(roomId);
      if (!participants) {
        participants = new Set();
        this.roomParticipants.set(roomId, participants);
      }
      
      participants.add(deviceId);
      
      // Update room participant count
      const room = this.rooms.get(roomId);
      room.participantCount = participants.size;
      room.lastActivity = Date.now();
      
      this.emit('participantJoined', {roomId, deviceId});
      Logger.debug(`RoomManager: Added participant ${deviceId} to room ${roomId}`);
      
      return true;
    } catch (error) {
      Logger.error(`RoomManager: Failed to add participant to room ${roomId}`, error);
      return false;
    }
  }

  /**
   * Remove participant from room
   */
  removeParticipantFromRoom(roomId, deviceId) {
    try {
      const participants = this.roomParticipants.get(roomId);
      if (participants) {
        participants.delete(deviceId);
        
        // Update room participant count
        const room = this.rooms.get(roomId);
        if (room) {
          room.participantCount = participants.size;
          room.lastActivity = Date.now();
        }
        
        this.emit('participantLeft', {roomId, deviceId});
        Logger.debug(`RoomManager: Removed participant ${deviceId} from room ${roomId}`);
        
        return true;
      }
      
      return false;
    } catch (error) {
      Logger.error(`RoomManager: Failed to remove participant from room ${roomId}`, error);
      return false;
    }
  }

  /**
   * Get all rooms
   */
  getAllRooms() {
    return Array.from(this.rooms.values());
  }

  /**
   * Get user's joined rooms
   */
  getUserRooms() {
    return Array.from(this.userRooms).map(roomId => this.rooms.get(roomId)).filter(Boolean);
  }

  /**
   * Get room by ID
   */
  getRoom(roomId) {
    return this.rooms.get(roomId);
  }

  /**
   * Search rooms by hashtag
   */
  searchRooms(query) {
    const normalizedQuery = query.toLowerCase();
    return Array.from(this.rooms.values()).filter(room => 
      room.hashtag.toLowerCase().includes(normalizedQuery) ||
      room.name.toLowerCase().includes(normalizedQuery) ||
      room.description.toLowerCase().includes(normalizedQuery)
    );
  }

  /**
   * Validate and normalize hashtag
   */
  validateAndNormalizeHashtag(hashtag) {
    if (!hashtag || typeof hashtag !== 'string') {
      throw new Error('Invalid hashtag');
    }
    
    // Ensure hashtag starts with #
    let normalized = hashtag.trim();
    if (!normalized.startsWith('#')) {
      normalized = '#' + normalized;
    }
    
    // Validate length
    if (normalized.length > ROOM_CONFIG.MAX_ROOM_NAME_LENGTH) {
      throw new Error('Hashtag too long');
    }
    
    // Validate characters (alphanumeric, underscore, hyphen)
    if (!/^#[a-zA-Z0-9_-]+$/.test(normalized)) {
      throw new Error('Invalid hashtag format');
    }
    
    return normalized.toLowerCase();
  }

  /**
   * Hash password for room protection
   */
  hashPassword(password) {
    return CryptoJS.SHA256(password).toString();
  }

  /**
   * Verify room password
   */
  verifyRoomPassword(room, password) {
    if (!room.isProtected) return true;
    if (!password) return false;
    
    const hashedPassword = this.hashPassword(password);
    return hashedPassword === room.passwordHash;
  }

  /**
   * Create default rooms
   */
  async createDefaultRooms() {
    try {
      for (const hashtag of ROOM_CONFIG.DEFAULT_ROOMS) {
        if (!this.rooms.has(hashtag)) {
          const room = await this.createRoom(hashtag);
          room.isDefault = true;
        }
      }
    } catch (error) {
      Logger.error('RoomManager: Failed to create default rooms', error);
    }
  }

  /**
   * Get current device ID
   */
  async getCurrentDeviceId() {
    // This should be implemented to return the current device's unique ID
    // For now, return a placeholder
    return 'current_device_id';
  }

  /**
   * Load rooms from storage
   */
  async loadRooms() {
    try {
      const roomsData = await AsyncStorage.getItem(this.storageKey);
      if (roomsData) {
        const rooms = JSON.parse(roomsData);
        for (const room of rooms) {
          this.rooms.set(room.id, room);
          this.roomParticipants.set(room.id, new Set());
          this.roomMessages.set(room.id, []);
        }
        Logger.debug(`RoomManager: Loaded ${rooms.length} rooms from storage`);
      }
    } catch (error) {
      Logger.error('RoomManager: Failed to load rooms from storage', error);
    }
  }

  /**
   * Load user rooms from storage
   */
  async loadUserRooms() {
    try {
      const userRoomsData = await AsyncStorage.getItem(this.userRoomsKey);
      if (userRoomsData) {
        const userRooms = JSON.parse(userRoomsData);
        this.userRooms = new Set(userRooms);
        Logger.debug(`RoomManager: Loaded ${userRooms.length} user rooms from storage`);
      }
    } catch (error) {
      Logger.error('RoomManager: Failed to load user rooms from storage', error);
    }
  }

  /**
   * Persist rooms to storage
   */
  async persistRooms() {
    try {
      const rooms = Array.from(this.rooms.values());
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(rooms));
    } catch (error) {
      Logger.error('RoomManager: Failed to persist rooms to storage', error);
    }
  }

  /**
   * Persist user rooms to storage
   */
  async persistUserRooms() {
    try {
      const userRooms = Array.from(this.userRooms);
      await AsyncStorage.setItem(this.userRoomsKey, JSON.stringify(userRooms));
    } catch (error) {
      Logger.error('RoomManager: Failed to persist user rooms to storage', error);
    }
  }

  /**
   * Get room statistics
   */
  getRoomStats() {
    return {
      totalRooms: this.rooms.size,
      joinedRooms: this.userRooms.size,
      defaultRooms: Array.from(this.rooms.values()).filter(room => room.isDefault).length,
      protectedRooms: Array.from(this.rooms.values()).filter(room => room.isProtected).length,
      totalParticipants: Array.from(this.roomParticipants.values()).reduce((sum, participants) => sum + participants.size, 0),
      totalMessages: Array.from(this.rooms.values()).reduce((sum, room) => sum + room.messageCount, 0),
    };
  }

  /**
   * Cleanup inactive rooms
   */
  async cleanup() {
    try {
      Logger.debug('RoomManager: Starting cleanup...');
      
      const now = Date.now();
      const inactiveThreshold = 24 * 60 * 60 * 1000; // 24 hours
      const roomsToRemove = [];
      
      // Find inactive rooms (not default rooms)
      for (const [roomId, room] of this.rooms) {
        if (!room.isDefault && room.participantCount === 0) {
          const inactiveTime = now - room.lastActivity;
          if (inactiveTime > inactiveThreshold) {
            roomsToRemove.push(roomId);
          }
        }
      }
      
      // Remove inactive rooms
      for (const roomId of roomsToRemove) {
        this.rooms.delete(roomId);
        this.roomParticipants.delete(roomId);
        this.roomMessages.delete(roomId);
      }
      
      if (roomsToRemove.length > 0) {
        await this.persistRooms();
        Logger.debug(`RoomManager: Cleanup completed, removed ${roomsToRemove.length} inactive rooms`);
      }
    } catch (error) {
      Logger.error('RoomManager: Cleanup failed', error);
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('RoomManager: Emergency cleanup initiated');
      
      // Clear all data
      this.rooms.clear();
      this.userRooms.clear();
      this.roomParticipants.clear();
      this.roomMessages.clear();
      
      // Remove from storage
      await AsyncStorage.removeItem(this.storageKey);
      await AsyncStorage.removeItem(this.userRoomsKey);
      
      Logger.warn('RoomManager: Emergency cleanup completed');
    } catch (error) {
      Logger.error('RoomManager: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown room manager
   */
  async shutdown() {
    try {
      Logger.info('RoomManager: Shutting down...');
      
      await this.persistRooms();
      await this.persistUserRooms();
      
      this.removeAllListeners();
      
      Logger.info('RoomManager: Shutdown complete');
    } catch (error) {
      Logger.error('RoomManager: Shutdown failed', error);
    }
  }
}

export default RoomManager;
