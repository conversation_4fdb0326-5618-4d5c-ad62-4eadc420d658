/**
 * NetworkTopology - Visualize mesh network structure
 * Shows connected devices and network topology
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import Logger from '@utils/Logger';

const NetworkTopology = ({meshManager}) => {
  const [topology, setTopology] = useState(null);
  const [networkStats, setNetworkStats] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadNetworkData();
    
    if (meshManager) {
      meshManager.on('topologyUpdated', handleTopologyUpdate);
      meshManager.on('deviceConnected', loadNetworkData);
      meshManager.on('deviceDisconnected', loadNetworkData);
    }
    
    return () => {
      if (meshManager) {
        meshManager.removeListener('topologyUpdated', handleTopologyUpdate);
        meshManager.removeListener('deviceConnected', loadNetworkData);
        meshManager.removeListener('deviceDisconnected', loadNetworkData);
      }
    };
  }, [meshManager]);

  /**
   * Load network data
   */
  const loadNetworkData = async () => {
    try {
      if (!meshManager) return;
      
      const networkTopology = meshManager.getNetworkTopology();
      const stats = meshManager.getNetworkStats();
      
      setTopology(networkTopology);
      setNetworkStats(stats);
      
      Logger.debug('NetworkTopology: Loaded network data', {
        nodeCount: networkTopology.nodes?.length || 0,
        stats,
      });
    } catch (error) {
      Logger.error('NetworkTopology: Failed to load network data', error);
    }
  };

  /**
   * Handle topology update
   */
  const handleTopologyUpdate = (newTopology) => {
    setTopology(newTopology);
  };

  /**
   * Refresh network data
   */
  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadNetworkData();
    setIsRefreshing(false);
  };

  /**
   * Render network statistics
   */
  const renderNetworkStats = () => {
    if (!networkStats) return null;
    
    return (
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Network Statistics</Text>
        
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{networkStats.nodeCount || 0}</Text>
            <Text style={styles.statLabel}>Connected Devices</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {networkStats.isScanning ? '🔍' : '⏸️'}
            </Text>
            <Text style={styles.statLabel}>Scanning</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {networkStats.isAdvertising ? '📡' : '⏸️'}
            </Text>
            <Text style={styles.statLabel}>Advertising</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{networkStats.routingTableSize || 0}</Text>
            <Text style={styles.statLabel}>Routes</Text>
          </View>
        </View>
      </View>
    );
  };

  /**
   * Render device list
   */
  const renderDeviceList = () => {
    if (!topology || !topology.nodes || topology.nodes.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No devices connected</Text>
          <Text style={styles.emptySubtext}>
            Make sure Bluetooth is enabled and other MeshTalk devices are nearby
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.devicesContainer}>
        <Text style={styles.sectionTitle}>Connected Devices</Text>
        
        {topology.nodes.map((device, index) => (
          <View key={device.id || index} style={styles.deviceItem}>
            <View style={styles.deviceHeader}>
              <Text style={styles.deviceName}>
                {device.name || `Device ${index + 1}`}
              </Text>
              <View style={[
                styles.statusIndicator,
                {backgroundColor: getDeviceStatusColor(device)}
              ]} />
            </View>
            
            <Text style={styles.deviceId}>
              ID: {device.id ? device.id.substring(0, 16) + '...' : 'Unknown'}
            </Text>
            
            <View style={styles.deviceStats}>
              {device.rssi && (
                <Text style={styles.deviceStat}>
                  📶 {device.rssi}dBm
                </Text>
              )}
              
              {device.hopCount && (
                <Text style={styles.deviceStat}>
                  🔗 {device.hopCount} hop{device.hopCount !== 1 ? 's' : ''}
                </Text>
              )}
              
              {device.lastSeen && (
                <Text style={styles.deviceStat}>
                  ⏰ {formatLastSeen(device.lastSeen)}
                </Text>
              )}
            </View>
            
            {device.connections && device.connections.size > 0 && (
              <Text style={styles.connectionInfo}>
                Connected to {device.connections.size} device{device.connections.size !== 1 ? 's' : ''}
              </Text>
            )}
          </View>
        ))}
      </View>
    );
  };

  /**
   * Get device status color
   */
  const getDeviceStatusColor = (device) => {
    if (!device.lastSeen) return '#00ff88'; // Assume online if no timestamp
    
    const now = Date.now();
    const timeDiff = now - device.lastSeen;
    
    if (timeDiff < 30000) return '#00ff88'; // Green - Online
    if (timeDiff < 300000) return '#ffaa00'; // Orange - Away
    return '#666'; // Gray - Offline
  };

  /**
   * Format last seen time
   */
  const formatLastSeen = (timestamp) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  /**
   * Render network topology visualization
   */
  const renderTopologyVisualization = () => {
    if (!topology || !topology.nodes || topology.nodes.length === 0) {
      return null;
    }
    
    return (
      <View style={styles.topologyContainer}>
        <Text style={styles.sectionTitle}>Network Topology</Text>
        
        <View style={styles.topologyVisualization}>
          <Text style={styles.topologyText}>
            📱 You are connected to {topology.nodes.length} device{topology.nodes.length !== 1 ? 's' : ''}
          </Text>
          
          <View style={styles.meshVisualization}>
            <View style={styles.centerNode}>
              <Text style={styles.centerNodeText}>You</Text>
            </View>
            
            {topology.nodes.slice(0, 6).map((device, index) => {
              const angle = (index * 60) * (Math.PI / 180); // 60 degrees apart
              const radius = 80;
              const x = Math.cos(angle) * radius;
              const y = Math.sin(angle) * radius;
              
              return (
                <View
                  key={device.id || index}
                  style={[
                    styles.connectedNode,
                    {
                      transform: [
                        {translateX: x},
                        {translateY: y},
                      ],
                    },
                  ]}
                >
                  <Text style={styles.connectedNodeText}>
                    {device.name ? device.name.substring(0, 3) : `D${index + 1}`}
                  </Text>
                </View>
              );
            })}
          </View>
          
          {topology.nodes.length > 6 && (
            <Text style={styles.moreDevicesText}>
              +{topology.nodes.length - 6} more device{topology.nodes.length - 6 !== 1 ? 's' : ''}
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          tintColor="#00ff88"
          colors={['#00ff88']}
        />
      }
    >
      {renderNetworkStats()}
      {renderTopologyVisualization()}
      {renderDeviceList()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  contentContainer: {
    padding: 16,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
  },
  statValue: {
    color: '#00ff88',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
  },
  topologyContainer: {
    marginBottom: 24,
  },
  topologyVisualization: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  topologyText: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
  },
  meshVisualization: {
    width: 200,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  centerNode: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#00ff88',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  centerNodeText: {
    color: '#000',
    fontSize: 10,
    fontWeight: 'bold',
  },
  connectedNode: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  connectedNodeText: {
    color: '#fff',
    fontSize: 8,
    fontWeight: 'bold',
  },
  moreDevicesText: {
    color: '#666',
    fontSize: 12,
    marginTop: 16,
    textAlign: 'center',
  },
  devicesContainer: {
    marginBottom: 24,
  },
  deviceItem: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  deviceName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  deviceId: {
    color: '#666',
    fontSize: 12,
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  deviceStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  deviceStat: {
    color: '#ccc',
    fontSize: 12,
    marginRight: 16,
    marginBottom: 4,
  },
  connectionInfo: {
    color: '#00ff88',
    fontSize: 12,
    fontStyle: 'italic',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    color: '#444',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default NetworkTopology;
