/**
 * Bluetooth Low Energy Constants
 * BLE specific configuration and UUIDs for MeshTalk
 */

// MeshTalk Service UUIDs (Custom 128-bit UUIDs)
export const BLE_SERVICES = {
  // Primary MeshTalk service
  MESHTALK_SERVICE: '6E400001-B5A3-F393-E0A9-E50E24DCCA9E',
  
  // Message transmission service
  MESSAGE_SERVICE: '6E400002-B5A3-F393-E0A9-E50E24DCCA9E',
  
  // Device discovery service
  DISCOVERY_SERVICE: '6E400003-B5A3-F393-E0A9-E50E24DCCA9E',
  
  // Network topology service
  TOPOLOGY_SERVICE: '6E400004-B5A3-F393-E0A9-E50E24DCCA9E',
};

// MeshTalk Characteristic UUIDs
export const BLE_CHARACTERISTICS = {
  // Message characteristics
  MESSAGE_TX: '6E400011-B5A3-F393-E0A9-E50E24DCCA9E', // Write
  MESSAGE_RX: '6E400012-B5A3-F393-E0A9-E50E24DCCA9E', // Notify
  MESSAGE_ACK: '6E400013-B5A3-F393-E0A9-E50E24DCCA9E', // Write/Notify
  
  // Device info characteristics
  DEVICE_ID: '6E400021-B5A3-F393-E0A9-E50E24DCCA9E', // Read
  DEVICE_NAME: '6E400022-B5A3-F393-E0A9-E50E24DCCA9E', // Read
  DEVICE_STATUS: '6E400023-B5A3-F393-E0A9-E50E24DCCA9E', // Read/Notify
  
  // Network characteristics
  NETWORK_TOPOLOGY: '6E400031-B5A3-F393-E0A9-E50E24DCCA9E', // Read/Notify
  ROUTING_TABLE: '6E400032-B5A3-F393-E0A9-E50E24DCCA9E', // Read/Write
  HEARTBEAT: '6E400033-B5A3-F393-E0A9-E50E24DCCA9E', // Notify
  
  // Security characteristics
  PUBLIC_KEY: '6E400041-B5A3-F393-E0A9-E50E24DCCA9E', // Read
  KEY_EXCHANGE: '6E400042-B5A3-F393-E0A9-E50E24DCCA9E', // Write
  ENCRYPTION_STATUS: '6E400043-B5A3-F393-E0A9-E50E24DCCA9E', // Read
};

// BLE Configuration
export const BLE_CONFIG = {
  // Scanning configuration
  SCAN_DURATION: 10000, // 10 seconds
  SCAN_INTERVAL: 5000, // 5 seconds between scans
  ALLOW_DUPLICATES: true,
  SCAN_MODE: 'balanced', // 'low_power', 'balanced', 'low_latency'
  
  // Connection configuration
  CONNECTION_PRIORITY: 'high', // 'low', 'balanced', 'high'
  MTU_SIZE: 512, // Maximum Transmission Unit
  CONNECTION_INTERVAL_MIN: 7.5, // ms
  CONNECTION_INTERVAL_MAX: 30, // ms
  SLAVE_LATENCY: 0,
  SUPERVISION_TIMEOUT: 6000, // ms
  
  // Advertising configuration
  ADVERTISING_INTERVAL_MIN: 100, // ms
  ADVERTISING_INTERVAL_MAX: 200, // ms
  ADVERTISING_TX_POWER: 'medium', // 'ultra_low', 'low', 'medium', 'high'
  CONNECTABLE: true,
  DISCOVERABLE: true,
  
  // Service configuration
  PRIMARY_SERVICE: true,
  INCLUDE_TX_POWER: true,
  INCLUDE_DEVICE_NAME: true,
  
  // Security configuration
  BONDING_REQUIRED: false,
  MITM_PROTECTION: false,
  SECURE_CONNECTIONS: true,
  KEYPRESS_NOTIFICATIONS: false,
};

// BLE Error Codes
export const BLE_ERRORS = {
  BLUETOOTH_DISABLED: 'BLUETOOTH_DISABLED',
  LOCATION_PERMISSION_DENIED: 'LOCATION_PERMISSION_DENIED',
  DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  SERVICE_NOT_FOUND: 'SERVICE_NOT_FOUND',
  CHARACTERISTIC_NOT_FOUND: 'CHARACTERISTIC_NOT_FOUND',
  WRITE_FAILED: 'WRITE_FAILED',
  READ_FAILED: 'READ_FAILED',
  NOTIFICATION_FAILED: 'NOTIFICATION_FAILED',
  SCAN_FAILED: 'SCAN_FAILED',
  ADVERTISING_FAILED: 'ADVERTISING_FAILED',
  MTU_REQUEST_FAILED: 'MTU_REQUEST_FAILED',
  BONDING_FAILED: 'BONDING_FAILED',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

// BLE States
export const BLE_STATES = {
  UNKNOWN: 'unknown',
  RESETTING: 'resetting',
  UNSUPPORTED: 'unsupported',
  UNAUTHORIZED: 'unauthorized',
  POWERED_OFF: 'powered_off',
  POWERED_ON: 'powered_on',
};

// Connection States
export const CONNECTION_STATES = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTING: 'disconnecting',
  DISCOVERING: 'discovering',
  READY: 'ready',
  ERROR: 'error',
};

// Device Types
export const DEVICE_TYPES = {
  UNKNOWN: 'unknown',
  PHONE: 'phone',
  TABLET: 'tablet',
  LAPTOP: 'laptop',
  DESKTOP: 'desktop',
  WEARABLE: 'wearable',
  IOT: 'iot',
};

// Message Types
export const MESSAGE_TYPES = {
  TEXT: 0x01,
  BINARY: 0x02,
  HEARTBEAT: 0x03,
  ACK: 0x04,
  NACK: 0x05,
  DISCOVERY: 0x06,
  TOPOLOGY: 0x07,
  KEY_EXCHANGE: 0x08,
  ROUTING: 0x09,
  STATUS: 0x0A,
};

// Protocol Versions
export const PROTOCOL_VERSION = {
  MAJOR: 1,
  MINOR: 0,
  PATCH: 0,
  STRING: '1.0.0',
};

export default {
  BLE_SERVICES,
  BLE_CHARACTERISTICS,
  BLE_CONFIG,
  BLE_ERRORS,
  BLE_STATES,
  CONNECTION_STATES,
  DEVICE_TYPES,
  MESSAGE_TYPES,
  PROTOCOL_VERSION,
};
