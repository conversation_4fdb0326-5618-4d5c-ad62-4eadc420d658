/**
 * MeshManager - Core Bluetooth Mesh Network Manager
 * Handles mesh network creation, maintenance, and coordination
 */

import {BleManager} from 'react-native-ble-plx';
import {EventEmitter} from 'events';
import DeviceDiscovery from './DeviceDiscovery';
import ConnectionManager from './ConnectionManager';
import MessageRouter from './MessageRouter';
import {BLE_CONFIG, BLE_STATES, CONNECTION_STATES} from '@constants/BluetoothConstants';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class MeshManager extends EventEmitter {
  constructor() {
    super();
    this.bleManager = new BleManager();
    this.deviceDiscovery = new DeviceDiscovery(this.bleManager);
    this.connectionManager = new ConnectionManager(this.bleManager);
    this.messageRouter = new MessageRouter();
    
    this.isInitialized = false;
    this.isScanning = false;
    this.isAdvertising = false;
    this.meshNodes = new Map(); // deviceId -> nodeInfo
    this.routingTable = new Map(); // destination -> nextHop
    this.networkTopology = new Map(); // deviceId -> connections
    
    this.heartbeatInterval = null;
    this.topologyUpdateInterval = null;
    
    this.setupEventListeners();
  }

  /**
   * Initialize the mesh manager
   */
  async initialize() {
    try {
      Logger.info('MeshManager: Initializing...');
      
      // Check Bluetooth state
      const state = await this.bleManager.state();
      if (state !== BLE_STATES.POWERED_ON) {
        throw new Error(`Bluetooth not available. State: ${state}`);
      }
      
      // Initialize components
      await this.deviceDiscovery.initialize();
      await this.connectionManager.initialize();
      await this.messageRouter.initialize();
      
      // Start mesh operations
      await this.startMeshOperations();
      
      this.isInitialized = true;
      this.emit('initialized');
      
      Logger.info('MeshManager: Initialized successfully');
    } catch (error) {
      Logger.error('MeshManager: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Start mesh network operations
   */
  async startMeshOperations() {
    try {
      // Start device discovery
      await this.startDiscovery();
      
      // Start advertising
      await this.startAdvertising();
      
      // Start heartbeat
      this.startHeartbeat();
      
      // Start topology updates
      this.startTopologyUpdates();
      
      Logger.info('MeshManager: Mesh operations started');
    } catch (error) {
      Logger.error('MeshManager: Failed to start mesh operations', error);
      throw error;
    }
  }

  /**
   * Start device discovery
   */
  async startDiscovery() {
    if (this.isScanning) return;
    
    try {
      this.isScanning = true;
      await this.deviceDiscovery.startScanning();
      Logger.info('MeshManager: Discovery started');
    } catch (error) {
      this.isScanning = false;
      Logger.error('MeshManager: Failed to start discovery', error);
      throw error;
    }
  }

  /**
   * Stop device discovery
   */
  async stopDiscovery() {
    if (!this.isScanning) return;
    
    try {
      await this.deviceDiscovery.stopScanning();
      this.isScanning = false;
      Logger.info('MeshManager: Discovery stopped');
    } catch (error) {
      Logger.error('MeshManager: Failed to stop discovery', error);
    }
  }

  /**
   * Start advertising
   */
  async startAdvertising() {
    if (this.isAdvertising) return;
    
    try {
      this.isAdvertising = true;
      await this.deviceDiscovery.startAdvertising();
      Logger.info('MeshManager: Advertising started');
    } catch (error) {
      this.isAdvertising = false;
      Logger.error('MeshManager: Failed to start advertising', error);
      throw error;
    }
  }

  /**
   * Stop advertising
   */
  async stopAdvertising() {
    if (!this.isAdvertising) return;
    
    try {
      await this.deviceDiscovery.stopAdvertising();
      this.isAdvertising = false;
      Logger.info('MeshManager: Advertising stopped');
    } catch (error) {
      Logger.error('MeshManager: Failed to stop advertising', error);
    }
  }

  /**
   * Connect to a discovered device
   */
  async connectToDevice(deviceId) {
    try {
      Logger.info(`MeshManager: Connecting to device ${deviceId}`);
      
      const device = await this.connectionManager.connect(deviceId);
      if (device) {
        await this.addMeshNode(device);
        this.emit('deviceConnected', device);
        return device;
      }
      
      throw new Error('Failed to connect to device');
    } catch (error) {
      Logger.error(`MeshManager: Failed to connect to device ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * Disconnect from a device
   */
  async disconnectFromDevice(deviceId) {
    try {
      Logger.info(`MeshManager: Disconnecting from device ${deviceId}`);
      
      await this.connectionManager.disconnect(deviceId);
      await this.removeMeshNode(deviceId);
      this.emit('deviceDisconnected', deviceId);
    } catch (error) {
      Logger.error(`MeshManager: Failed to disconnect from device ${deviceId}`, error);
    }
  }

  /**
   * Add a device to the mesh network
   */
  async addMeshNode(device) {
    const nodeInfo = {
      id: device.id,
      name: device.name || 'Unknown',
      rssi: device.rssi || -100,
      lastSeen: Date.now(),
      connections: new Set(),
      isRelay: true,
      hopCount: 1,
    };
    
    this.meshNodes.set(device.id, nodeInfo);
    this.updateRoutingTable();
    this.updateNetworkTopology();
    
    Logger.info(`MeshManager: Added mesh node ${device.id}`);
    this.emit('meshNodeAdded', nodeInfo);
  }

  /**
   * Remove a device from the mesh network
   */
  async removeMeshNode(deviceId) {
    if (this.meshNodes.has(deviceId)) {
      const nodeInfo = this.meshNodes.get(deviceId);
      this.meshNodes.delete(deviceId);
      
      // Update routing table
      this.updateRoutingTable();
      this.updateNetworkTopology();
      
      Logger.info(`MeshManager: Removed mesh node ${deviceId}`);
      this.emit('meshNodeRemoved', nodeInfo);
    }
  }

  /**
   * Send a message through the mesh network
   */
  async sendMessage(message, targetDeviceId = null) {
    try {
      const route = targetDeviceId 
        ? this.findRoute(targetDeviceId)
        : this.getBroadcastTargets();
      
      if (!route || route.length === 0) {
        throw new Error('No route available for message delivery');
      }
      
      const result = await this.messageRouter.routeMessage(message, route);
      this.emit('messageSent', {message, route, result});
      
      return result;
    } catch (error) {
      Logger.error('MeshManager: Failed to send message', error);
      this.emit('messageError', {message, error});
      throw error;
    }
  }

  /**
   * Find route to target device
   */
  findRoute(targetDeviceId) {
    if (this.meshNodes.has(targetDeviceId)) {
      // Direct connection available
      return [targetDeviceId];
    }
    
    // Use routing table for multi-hop
    const nextHop = this.routingTable.get(targetDeviceId);
    if (nextHop) {
      return [nextHop];
    }
    
    // No route found
    return null;
  }

  /**
   * Get broadcast targets (all connected devices)
   */
  getBroadcastTargets() {
    return Array.from(this.meshNodes.keys());
  }

  /**
   * Update routing table based on network topology
   */
  updateRoutingTable() {
    this.routingTable.clear();
    
    // Simple routing: direct connections first
    for (const [deviceId, nodeInfo] of this.meshNodes) {
      this.routingTable.set(deviceId, deviceId);
    }
    
    // TODO: Implement more sophisticated routing algorithms
    // (Dijkstra, AODV, etc.) for multi-hop routing
  }

  /**
   * Update network topology
   */
  updateNetworkTopology() {
    this.networkTopology.clear();
    
    for (const [deviceId, nodeInfo] of this.meshNodes) {
      this.networkTopology.set(deviceId, Array.from(nodeInfo.connections));
    }
    
    this.emit('topologyUpdated', this.getNetworkTopology());
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, APP_CONFIG.MESH.HEARTBEAT_INTERVAL);
  }

  /**
   * Send heartbeat to all connected devices
   */
  async sendHeartbeat() {
    try {
      const heartbeatMessage = {
        type: 'heartbeat',
        timestamp: Date.now(),
        deviceId: await this.getDeviceId(),
        topology: this.getNetworkTopology(),
      };
      
      await this.sendMessage(heartbeatMessage);
    } catch (error) {
      Logger.error('MeshManager: Failed to send heartbeat', error);
    }
  }

  /**
   * Start topology updates
   */
  startTopologyUpdates() {
    if (this.topologyUpdateInterval) {
      clearInterval(this.topologyUpdateInterval);
    }
    
    this.topologyUpdateInterval = setInterval(() => {
      this.updateNetworkTopology();
    }, APP_CONFIG.MESH.MESH_REFRESH_INTERVAL);
  }

  /**
   * Get current network topology
   */
  getNetworkTopology() {
    const topology = {
      nodes: Array.from(this.meshNodes.values()),
      connections: Array.from(this.networkTopology.entries()),
      routingTable: Array.from(this.routingTable.entries()),
      timestamp: Date.now(),
    };
    
    return topology;
  }

  /**
   * Get device ID
   */
  async getDeviceId() {
    // Implementation depends on device identification strategy
    return this.connectionManager.getDeviceId();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Device discovery events
    this.deviceDiscovery.on('deviceDiscovered', (device) => {
      this.emit('deviceDiscovered', device);
    });
    
    this.deviceDiscovery.on('deviceLost', (deviceId) => {
      this.removeMeshNode(deviceId);
    });
    
    // Connection events
    this.connectionManager.on('deviceConnected', (device) => {
      this.addMeshNode(device);
    });
    
    this.connectionManager.on('deviceDisconnected', (deviceId) => {
      this.removeMeshNode(deviceId);
    });
    
    // Message routing events
    this.messageRouter.on('messageReceived', (message) => {
      this.emit('messageReceived', message);
    });
    
    this.messageRouter.on('messageForwarded', (message) => {
      this.emit('messageForwarded', message);
    });
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    try {
      Logger.info('MeshManager: Shutting down...');
      
      // Clear intervals
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = null;
      }
      
      if (this.topologyUpdateInterval) {
        clearInterval(this.topologyUpdateInterval);
        this.topologyUpdateInterval = null;
      }
      
      // Stop operations
      await this.stopDiscovery();
      await this.stopAdvertising();
      
      // Disconnect all devices
      for (const deviceId of this.meshNodes.keys()) {
        await this.disconnectFromDevice(deviceId);
      }
      
      // Cleanup components
      await this.connectionManager.shutdown();
      await this.deviceDiscovery.shutdown();
      await this.messageRouter.shutdown();
      
      this.isInitialized = false;
      this.emit('shutdown');
      
      Logger.info('MeshManager: Shutdown complete');
    } catch (error) {
      Logger.error('MeshManager: Shutdown failed', error);
    }
  }

  /**
   * Get mesh network statistics
   */
  getNetworkStats() {
    return {
      nodeCount: this.meshNodes.size,
      isScanning: this.isScanning,
      isAdvertising: this.isAdvertising,
      routingTableSize: this.routingTable.size,
      topologySize: this.networkTopology.size,
      lastUpdate: Date.now(),
    };
  }
}

export default MeshManager;
