{"name": "meshtalk", "version": "1.0.0", "description": "Decentralized Bluetooth mesh messaging app with end-to-end encryption", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "clean": "react-native clean", "clean:android": "cd android && ./gradlew clean && cd ..", "clean:ios": "cd ios && xcodebuild clean && cd ..", "build:android": "cd android && ./gradlew assembleRelease && cd ..", "build:ios": "cd ios && xcodebuild -workspace MeshTalk.xcworkspace -scheme MeshTalk -configuration Release archive && cd ..", "postinstall": "cd ios && pod install && cd .."}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-ble-manager": "^10.1.2", "react-native-ble-plx": "^3.0.2", "react-native-vector-icons": "^10.0.0", "react-native-async-storage": "^1.19.3", "react-native-keychain": "^8.1.3", "react-native-crypto-js": "^1.0.0", "react-native-randombytes": "^3.6.1", "react-native-get-random-values": "^1.9.0", "react-native-sodium": "^0.8.1", "react-native-background-job": "^1.2.4", "react-native-permissions": "^3.9.3", "react-native-device-info": "^10.11.0", "react-native-haptic-feedback": "^2.2.0", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.25.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-svg": "^13.14.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-uuid": "^2.0.1", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "stream-browserify": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "@testing-library/react-native": "^12.3.2", "@testing-library/jest-native": "^5.4.3", "detox": "^20.13.5"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-ble-manager|react-native-ble-plx|react-native-vector-icons|react-native-async-storage|react-native-keychain|react-native-crypto-js|react-native-randombytes|react-native-get-random-values|react-native-sodium|react-native-background-job|react-native-permissions|react-native-device-info|react-native-haptic-feedback|react-native-gesture-handler|react-native-reanimated|react-native-safe-area-context|react-native-screens|react-native-svg|react-native-linear-gradient|react-native-modal|react-native-toast-message|react-native-uuid)/)"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/index.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/MeshTalk.git"}, "keywords": ["bluetooth", "mesh-network", "decentralized", "messaging", "privacy", "encryption", "p2p", "offline", "react-native", "mobile"], "author": {"name": "HectorTa1989", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "bugs": {"url": "https://github.com/HectorTa1989/MeshTalk/issues"}, "homepage": "https://github.com/HectorTa1989/MeshTalk#readme"}