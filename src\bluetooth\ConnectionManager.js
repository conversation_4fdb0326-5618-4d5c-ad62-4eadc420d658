/**
 * ConnectionManager - Bluetooth connection management
 * Handles device connections, service discovery, and characteristic operations
 */

import {EventEmitter} from 'events';
import DeviceInfo from 'react-native-device-info';
import {
  BLE_SERVICES,
  BLE_CHARACTERISTICS,
  BLE_CONFIG,
  CONNECTION_STATES,
  BLE_ERRORS,
} from '@constants/BluetoothConstants';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class ConnectionManager extends EventEmitter {
  constructor(bleManager) {
    super();
    this.bleManager = bleManager;
    this.connections = new Map(); // deviceId -> connectionInfo
    this.deviceId = null;
    this.deviceName = null;
  }

  /**
   * Initialize connection manager
   */
  async initialize() {
    try {
      Logger.info('ConnectionManager: Initializing...');
      
      // Get device information
      this.deviceId = await DeviceInfo.getUniqueId();
      this.deviceName = await DeviceInfo.getDeviceName();
      
      Logger.info(`ConnectionManager: Device ID: ${this.deviceId}, Name: ${this.deviceName}`);
      Logger.info('ConnectionManager: Initialized successfully');
    } catch (error) {
      Logger.error('ConnectionManager: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Connect to a device
   */
  async connect(deviceId) {
    if (this.connections.has(deviceId)) {
      const connection = this.connections.get(deviceId);
      if (connection.state === CONNECTION_STATES.CONNECTED) {
        Logger.warn(`ConnectionManager: Already connected to ${deviceId}`);
        return connection.device;
      }
    }

    try {
      Logger.info(`ConnectionManager: Connecting to ${deviceId}...`);
      
      // Update connection state
      this.updateConnectionState(deviceId, CONNECTION_STATES.CONNECTING);
      
      // Connect to device
      const device = await this.bleManager.connectToDevice(deviceId, {
        requestMTU: BLE_CONFIG.MTU_SIZE,
        connectionPriority: BLE_CONFIG.CONNECTION_PRIORITY,
        timeout: APP_CONFIG.MESH.CONNECTION_TIMEOUT,
      });
      
      // Discover services
      await device.discoverAllServicesAndCharacteristics();
      
      // Setup connection info
      const connectionInfo = {
        device,
        deviceId,
        state: CONNECTION_STATES.CONNECTED,
        connectedAt: Date.now(),
        lastActivity: Date.now(),
        services: new Map(),
        characteristics: new Map(),
        subscriptions: new Map(),
      };
      
      // Discover MeshTalk services
      await this.discoverMeshTalkServices(connectionInfo);
      
      // Setup characteristic notifications
      await this.setupNotifications(connectionInfo);
      
      // Store connection
      this.connections.set(deviceId, connectionInfo);
      
      // Setup disconnect handler
      device.onDisconnected((error, disconnectedDevice) => {
        this.handleDeviceDisconnected(disconnectedDevice.id, error);
      });
      
      this.updateConnectionState(deviceId, CONNECTION_STATES.READY);
      this.emit('deviceConnected', device);
      
      Logger.info(`ConnectionManager: Connected to ${deviceId}`);
      return device;
    } catch (error) {
      Logger.error(`ConnectionManager: Failed to connect to ${deviceId}`, error);
      this.updateConnectionState(deviceId, CONNECTION_STATES.ERROR);
      this.emit('connectionError', {deviceId, error});
      throw error;
    }
  }

  /**
   * Disconnect from a device
   */
  async disconnect(deviceId) {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      Logger.warn(`ConnectionManager: No connection found for ${deviceId}`);
      return;
    }

    try {
      Logger.info(`ConnectionManager: Disconnecting from ${deviceId}...`);
      
      this.updateConnectionState(deviceId, CONNECTION_STATES.DISCONNECTING);
      
      // Cleanup subscriptions
      await this.cleanupSubscriptions(connection);
      
      // Disconnect device
      await connection.device.cancelConnection();
      
      // Remove connection
      this.connections.delete(deviceId);
      
      this.emit('deviceDisconnected', deviceId);
      Logger.info(`ConnectionManager: Disconnected from ${deviceId}`);
    } catch (error) {
      Logger.error(`ConnectionManager: Failed to disconnect from ${deviceId}`, error);
      // Force remove connection even if disconnect fails
      this.connections.delete(deviceId);
    }
  }

  /**
   * Discover MeshTalk services and characteristics
   */
  async discoverMeshTalkServices(connectionInfo) {
    try {
      const {device} = connectionInfo;
      
      // Discover MeshTalk service
      const service = await device.services();
      const meshTalkService = service.find(s => s.uuid === BLE_SERVICES.MESHTALK_SERVICE);
      
      if (!meshTalkService) {
        throw new Error('MeshTalk service not found');
      }
      
      connectionInfo.services.set(BLE_SERVICES.MESHTALK_SERVICE, meshTalkService);
      
      // Discover characteristics
      const characteristics = await meshTalkService.characteristics();
      
      for (const characteristic of characteristics) {
        connectionInfo.characteristics.set(characteristic.uuid, characteristic);
        Logger.debug(`ConnectionManager: Found characteristic ${characteristic.uuid}`);
      }
      
      Logger.info(`ConnectionManager: Discovered ${characteristics.length} characteristics`);
    } catch (error) {
      Logger.error('ConnectionManager: Service discovery failed', error);
      throw error;
    }
  }

  /**
   * Setup characteristic notifications
   */
  async setupNotifications(connectionInfo) {
    try {
      const notificationCharacteristics = [
        BLE_CHARACTERISTICS.MESSAGE_RX,
        BLE_CHARACTERISTICS.MESSAGE_ACK,
        BLE_CHARACTERISTICS.DEVICE_STATUS,
        BLE_CHARACTERISTICS.NETWORK_TOPOLOGY,
        BLE_CHARACTERISTICS.HEARTBEAT,
      ];

      for (const charUuid of notificationCharacteristics) {
        const characteristic = connectionInfo.characteristics.get(charUuid);
        if (characteristic && characteristic.isNotifiable) {
          const subscription = characteristic.monitor((error, char) => {
            if (error) {
              Logger.error(`ConnectionManager: Notification error for ${charUuid}`, error);
            } else {
              this.handleCharacteristicNotification(connectionInfo.deviceId, char);
            }
          });
          
          connectionInfo.subscriptions.set(charUuid, subscription);
          Logger.debug(`ConnectionManager: Setup notification for ${charUuid}`);
        }
      }
    } catch (error) {
      Logger.error('ConnectionManager: Failed to setup notifications', error);
    }
  }

  /**
   * Handle characteristic notification
   */
  handleCharacteristicNotification(deviceId, characteristic) {
    try {
      const data = characteristic.value;
      if (!data) return;
      
      const decodedData = Buffer.from(data, 'base64');
      
      this.emit('characteristicNotification', {
        deviceId,
        characteristicUuid: characteristic.uuid,
        data: decodedData,
        timestamp: Date.now(),
      });
      
      // Update last activity
      const connection = this.connections.get(deviceId);
      if (connection) {
        connection.lastActivity = Date.now();
      }
    } catch (error) {
      Logger.error('ConnectionManager: Failed to handle notification', error);
    }
  }

  /**
   * Write data to characteristic
   */
  async writeCharacteristic(deviceId, characteristicUuid, data) {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.state !== CONNECTION_STATES.READY) {
      throw new Error(`Device ${deviceId} not ready for write operations`);
    }

    try {
      const characteristic = connection.characteristics.get(characteristicUuid);
      if (!characteristic) {
        throw new Error(`Characteristic ${characteristicUuid} not found`);
      }

      const base64Data = Buffer.from(data).toString('base64');
      await characteristic.writeWithResponse(base64Data);
      
      // Update last activity
      connection.lastActivity = Date.now();
      
      Logger.debug(`ConnectionManager: Wrote ${data.length} bytes to ${characteristicUuid}`);
    } catch (error) {
      Logger.error(`ConnectionManager: Failed to write to ${characteristicUuid}`, error);
      throw error;
    }
  }

  /**
   * Read data from characteristic
   */
  async readCharacteristic(deviceId, characteristicUuid) {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.state !== CONNECTION_STATES.READY) {
      throw new Error(`Device ${deviceId} not ready for read operations`);
    }

    try {
      const characteristic = connection.characteristics.get(characteristicUuid);
      if (!characteristic) {
        throw new Error(`Characteristic ${characteristicUuid} not found`);
      }

      const result = await characteristic.read();
      const data = Buffer.from(result.value, 'base64');
      
      // Update last activity
      connection.lastActivity = Date.now();
      
      Logger.debug(`ConnectionManager: Read ${data.length} bytes from ${characteristicUuid}`);
      return data;
    } catch (error) {
      Logger.error(`ConnectionManager: Failed to read from ${characteristicUuid}`, error);
      throw error;
    }
  }

  /**
   * Handle device disconnected
   */
  handleDeviceDisconnected(deviceId, error) {
    Logger.info(`ConnectionManager: Device ${deviceId} disconnected`, error);
    
    const connection = this.connections.get(deviceId);
    if (connection) {
      // Cleanup subscriptions
      this.cleanupSubscriptions(connection);
      
      // Remove connection
      this.connections.delete(deviceId);
    }
    
    this.emit('deviceDisconnected', deviceId);
  }

  /**
   * Cleanup subscriptions for a connection
   */
  async cleanupSubscriptions(connection) {
    try {
      for (const [uuid, subscription] of connection.subscriptions) {
        subscription.remove();
        Logger.debug(`ConnectionManager: Removed subscription for ${uuid}`);
      }
      connection.subscriptions.clear();
    } catch (error) {
      Logger.error('ConnectionManager: Failed to cleanup subscriptions', error);
    }
  }

  /**
   * Update connection state
   */
  updateConnectionState(deviceId, state) {
    const connection = this.connections.get(deviceId);
    if (connection) {
      connection.state = state;
    } else {
      this.connections.set(deviceId, {
        deviceId,
        state,
        device: null,
        connectedAt: null,
        lastActivity: Date.now(),
        services: new Map(),
        characteristics: new Map(),
        subscriptions: new Map(),
      });
    }
    
    this.emit('connectionStateChanged', {deviceId, state});
  }

  /**
   * Get connection info
   */
  getConnection(deviceId) {
    return this.connections.get(deviceId);
  }

  /**
   * Get all connections
   */
  getAllConnections() {
    return Array.from(this.connections.values());
  }

  /**
   * Get connected device IDs
   */
  getConnectedDeviceIds() {
    return Array.from(this.connections.keys()).filter(deviceId => {
      const connection = this.connections.get(deviceId);
      return connection && connection.state === CONNECTION_STATES.READY;
    });
  }

  /**
   * Check if device is connected
   */
  isConnected(deviceId) {
    const connection = this.connections.get(deviceId);
    return connection && connection.state === CONNECTION_STATES.READY;
  }

  /**
   * Get device ID
   */
  getDeviceId() {
    return this.deviceId;
  }

  /**
   * Get device name
   */
  getDeviceName() {
    return this.deviceName;
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    try {
      Logger.info('ConnectionManager: Shutting down...');
      
      // Disconnect all devices
      const deviceIds = Array.from(this.connections.keys());
      for (const deviceId of deviceIds) {
        await this.disconnect(deviceId);
      }
      
      this.removeAllListeners();
      
      Logger.info('ConnectionManager: Shutdown complete');
    } catch (error) {
      Logger.error('ConnectionManager: Shutdown failed', error);
    }
  }
}

export default ConnectionManager;
