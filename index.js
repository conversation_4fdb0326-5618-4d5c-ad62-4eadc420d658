/**
 * MeshTalk - Decentralized Bluetooth Mesh Messaging
 * Entry point for React Native application
 */

import {AppRegistry} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';

// Polyfills for crypto functionality
import 'react-native-get-random-values';
import {Buffer} from 'buffer';
global.Buffer = Buffer;

AppRegistry.registerComponent(appName, () => App);
