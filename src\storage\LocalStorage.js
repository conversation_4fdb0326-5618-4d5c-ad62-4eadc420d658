/**
 * LocalStorage - Encrypted local storage management
 * Handles secure data storage with encryption and auto-deletion
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import EncryptionEngine from '@crypto/EncryptionEngine';
import {randomBytes} from 'react-native-randombytes';
import {APP_CONFIG} from '@constants/Config';
import {CRYPTO_CONFIG} from '@constants/CryptoConstants';
import Logger from '@utils/Logger';

class LocalStorage {
  constructor() {
    this.encryptionEngine = new EncryptionEngine();
    this.storageKey = 'meshtalk_encrypted_storage';
    this.masterKey = null;
    this.isInitialized = false;
    this.cleanupInterval = null;
  }

  /**
   * Initialize local storage
   */
  async initialize() {
    try {
      Logger.info('LocalStorage: Initializing...');
      
      // Load or generate master key
      await this.loadOrGenerateMasterKey();
      
      // Start cleanup interval
      this.startCleanupInterval();
      
      this.isInitialized = true;
      Logger.info('LocalStorage: Initialized successfully');
    } catch (error) {
      Logger.error('LocalStorage: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Store encrypted data
   */
  async setItem(key, value, ttl = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('LocalStorage not initialized');
      }

      const data = {
        value,
        timestamp: Date.now(),
        ttl: ttl || APP_CONFIG.STORAGE.DEFAULT_TTL,
        encrypted: true,
      };

      // Serialize data
      const serializedData = JSON.stringify(data);
      
      // Encrypt data
      const encryptedData = await this.encryptionEngine.encrypt(
        Buffer.from(serializedData, 'utf8'),
        this.masterKey
      );

      // Store encrypted data
      const storageData = {
        data: encryptedData.toString('base64'),
        timestamp: Date.now(),
      };

      await AsyncStorage.setItem(
        this.getStorageKey(key),
        JSON.stringify(storageData)
      );

      Logger.debug(`LocalStorage: Stored encrypted item ${key}`);
      return true;
    } catch (error) {
      Logger.error(`LocalStorage: Failed to store item ${key}`, error);
      throw error;
    }
  }

  /**
   * Retrieve and decrypt data
   */
  async getItem(key) {
    try {
      if (!this.isInitialized) {
        throw new Error('LocalStorage not initialized');
      }

      const storageData = await AsyncStorage.getItem(this.getStorageKey(key));
      if (!storageData) {
        return null;
      }

      const parsedStorageData = JSON.parse(storageData);
      
      // Decrypt data
      const encryptedData = Buffer.from(parsedStorageData.data, 'base64');
      const decryptedData = await this.encryptionEngine.decrypt(
        encryptedData,
        this.masterKey
      );

      // Parse decrypted data
      const data = JSON.parse(decryptedData.toString('utf8'));
      
      // Check TTL
      if (this.isExpired(data)) {
        await this.removeItem(key);
        return null;
      }

      Logger.debug(`LocalStorage: Retrieved encrypted item ${key}`);
      return data.value;
    } catch (error) {
      Logger.error(`LocalStorage: Failed to retrieve item ${key}`, error);
      return null;
    }
  }

  /**
   * Remove item from storage
   */
  async removeItem(key) {
    try {
      await AsyncStorage.removeItem(this.getStorageKey(key));
      Logger.debug(`LocalStorage: Removed item ${key}`);
      return true;
    } catch (error) {
      Logger.error(`LocalStorage: Failed to remove item ${key}`, error);
      return false;
    }
  }

  /**
   * Check if item exists
   */
  async hasItem(key) {
    try {
      const item = await this.getItem(key);
      return item !== null;
    } catch (error) {
      Logger.error(`LocalStorage: Failed to check item ${key}`, error);
      return false;
    }
  }

  /**
   * Get all keys
   */
  async getAllKeys() {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const prefix = this.getStorageKey('');
      
      return allKeys
        .filter(key => key.startsWith(prefix))
        .map(key => key.replace(prefix, ''));
    } catch (error) {
      Logger.error('LocalStorage: Failed to get all keys', error);
      return [];
    }
  }

  /**
   * Get multiple items
   */
  async getMultipleItems(keys) {
    try {
      const results = {};
      
      for (const key of keys) {
        results[key] = await this.getItem(key);
      }
      
      return results;
    } catch (error) {
      Logger.error('LocalStorage: Failed to get multiple items', error);
      return {};
    }
  }

  /**
   * Set multiple items
   */
  async setMultipleItems(items, ttl = null) {
    try {
      const results = {};
      
      for (const [key, value] of Object.entries(items)) {
        results[key] = await this.setItem(key, value, ttl);
      }
      
      return results;
    } catch (error) {
      Logger.error('LocalStorage: Failed to set multiple items', error);
      throw error;
    }
  }

  /**
   * Clear all stored data
   */
  async clear() {
    try {
      const keys = await this.getAllKeys();
      
      for (const key of keys) {
        await this.removeItem(key);
      }
      
      Logger.info(`LocalStorage: Cleared ${keys.length} items`);
      return true;
    } catch (error) {
      Logger.error('LocalStorage: Failed to clear storage', error);
      return false;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    try {
      const keys = await this.getAllKeys();
      let totalSize = 0;
      let expiredCount = 0;
      
      for (const key of keys) {
        try {
          const storageData = await AsyncStorage.getItem(this.getStorageKey(key));
          if (storageData) {
            totalSize += storageData.length;
            
            // Check if expired
            const parsedData = JSON.parse(storageData);
            const decryptedData = await this.encryptionEngine.decrypt(
              Buffer.from(parsedData.data, 'base64'),
              this.masterKey
            );
            const data = JSON.parse(decryptedData.toString('utf8'));
            
            if (this.isExpired(data)) {
              expiredCount++;
            }
          }
        } catch (error) {
          // Skip corrupted items
        }
      }
      
      return {
        totalItems: keys.length,
        totalSize,
        expiredItems: expiredCount,
        averageItemSize: keys.length > 0 ? totalSize / keys.length : 0,
      };
    } catch (error) {
      Logger.error('LocalStorage: Failed to get storage stats', error);
      return {
        totalItems: 0,
        totalSize: 0,
        expiredItems: 0,
        averageItemSize: 0,
      };
    }
  }

  /**
   * Load or generate master key
   */
  async loadOrGenerateMasterKey() {
    try {
      // Try to load existing master key
      const storedKey = await AsyncStorage.getItem('meshtalk_master_key');
      
      if (storedKey) {
        this.masterKey = Buffer.from(storedKey, 'base64');
        Logger.info('LocalStorage: Loaded existing master key');
      } else {
        // Generate new master key
        this.masterKey = await this.generateMasterKey();
        await AsyncStorage.setItem('meshtalk_master_key', this.masterKey.toString('base64'));
        Logger.info('LocalStorage: Generated new master key');
      }
    } catch (error) {
      Logger.error('LocalStorage: Failed to load/generate master key', error);
      throw error;
    }
  }

  /**
   * Generate master key
   */
  async generateMasterKey() {
    return new Promise((resolve, reject) => {
      randomBytes(CRYPTO_CONFIG.SYMMETRIC_KEY_SIZE, (error, bytes) => {
        if (error) {
          reject(error);
        } else {
          resolve(bytes);
        }
      });
    });
  }

  /**
   * Get storage key with prefix
   */
  getStorageKey(key) {
    return `${this.storageKey}_${key}`;
  }

  /**
   * Check if data is expired
   */
  isExpired(data) {
    if (!data.ttl) return false;
    
    const age = Date.now() - data.timestamp;
    return age > data.ttl;
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, APP_CONFIG.STORAGE.CLEANUP_INTERVAL);
  }

  /**
   * Cleanup expired items
   */
  async cleanup() {
    try {
      Logger.debug('LocalStorage: Starting cleanup...');
      
      const keys = await this.getAllKeys();
      let cleanedCount = 0;
      
      for (const key of keys) {
        try {
          const item = await this.getItem(key);
          if (item === null) {
            // Item was expired and removed
            cleanedCount++;
          }
        } catch (error) {
          // Remove corrupted items
          await this.removeItem(key);
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        Logger.debug(`LocalStorage: Cleanup completed, removed ${cleanedCount} items`);
      }
    } catch (error) {
      Logger.error('LocalStorage: Cleanup failed', error);
    }
  }

  /**
   * Backup storage data
   */
  async backup() {
    try {
      Logger.info('LocalStorage: Starting backup...');
      
      const keys = await this.getAllKeys();
      const backupData = {};
      
      for (const key of keys) {
        const value = await this.getItem(key);
        if (value !== null) {
          backupData[key] = value;
        }
      }
      
      const backup = {
        timestamp: Date.now(),
        version: APP_CONFIG.VERSION,
        data: backupData,
      };
      
      Logger.info(`LocalStorage: Backup completed with ${Object.keys(backupData).length} items`);
      return backup;
    } catch (error) {
      Logger.error('LocalStorage: Backup failed', error);
      throw error;
    }
  }

  /**
   * Restore from backup
   */
  async restore(backupData) {
    try {
      Logger.info('LocalStorage: Starting restore...');
      
      if (!backupData.data) {
        throw new Error('Invalid backup data');
      }
      
      // Clear existing data
      await this.clear();
      
      // Restore data
      let restoredCount = 0;
      for (const [key, value] of Object.entries(backupData.data)) {
        try {
          await this.setItem(key, value);
          restoredCount++;
        } catch (error) {
          Logger.warn(`LocalStorage: Failed to restore item ${key}`, error);
        }
      }
      
      Logger.info(`LocalStorage: Restore completed, restored ${restoredCount} items`);
      return restoredCount;
    } catch (error) {
      Logger.error('LocalStorage: Restore failed', error);
      throw error;
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('LocalStorage: Emergency cleanup initiated');
      
      // Clear all data
      await this.clear();
      
      // Remove master key
      await AsyncStorage.removeItem('meshtalk_master_key');
      
      // Clear master key from memory
      if (this.masterKey) {
        this.masterKey.fill(0);
        this.masterKey = null;
      }
      
      Logger.warn('LocalStorage: Emergency cleanup completed');
    } catch (error) {
      Logger.error('LocalStorage: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown local storage
   */
  async shutdown() {
    try {
      Logger.info('LocalStorage: Shutting down...');
      
      // Clear cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
      }
      
      // Secure cleanup of master key
      if (this.masterKey) {
        this.masterKey.fill(0);
        this.masterKey = null;
      }
      
      this.isInitialized = false;
      
      Logger.info('LocalStorage: Shutdown complete');
    } catch (error) {
      Logger.error('LocalStorage: Shutdown failed', error);
    }
  }
}

export default LocalStorage;
