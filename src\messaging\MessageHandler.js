/**
 * MessageHandler - Core message processing and management
 * Handles message creation, encryption, decryption, and validation
 */

import {EventEmitter} from 'events';
import {v4 as uuidv4} from 'react-native-uuid';
import EncryptionEngine from '@crypto/EncryptionEngine';
import KeyManager from '@crypto/KeyManager';
import MessageQueue from './MessageQueue';
import DeliveryTracker from './DeliveryTracker';
import {
  MESSAGE_TYPES,
  PROTOCOL_CONSTANTS,
  CRYPTO_ERRORS,
} from '@constants/BluetoothConstants';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class MessageHandler extends EventEmitter {
  constructor() {
    super();
    this.encryptionEngine = new EncryptionEngine();
    this.keyManager = new KeyManager();
    this.messageQueue = new MessageQueue();
    this.deliveryTracker = new DeliveryTracker();
    
    this.messageCache = new Map(); // messageId -> messageInfo
    this.duplicateFilter = new Set(); // messageId set for duplicate detection
    this.processingQueue = [];
    this.isProcessing = false;
  }

  /**
   * Initialize message handler
   */
  async initialize() {
    try {
      Logger.info('MessageHandler: Initializing...');
      
      await this.keyManager.initialize();
      await this.messageQueue.initialize();
      await this.deliveryTracker.initialize();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Start message processing
      this.startMessageProcessing();
      
      Logger.info('MessageHandler: Initialized successfully');
    } catch (error) {
      Logger.error('MessageHandler: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Create and send a text message
   */
  async sendTextMessage(content, targetDeviceId = null, roomId = null) {
    try {
      const message = {
        id: uuidv4(),
        type: MESSAGE_TYPES.TEXT,
        content,
        targetDeviceId,
        roomId,
        senderId: this.keyManager.getIdentityPublicKey(),
        timestamp: Date.now(),
        ttl: APP_CONFIG.MESSAGING.MESSAGE_TTL,
      };

      return await this.sendMessage(message);
    } catch (error) {
      Logger.error('MessageHandler: Failed to send text message', error);
      throw error;
    }
  }

  /**
   * Send a message
   */
  async sendMessage(message) {
    try {
      Logger.info(`MessageHandler: Sending message ${message.id}`);
      
      // Validate message
      this.validateMessage(message);
      
      // Encrypt message if target is specified
      let processedMessage = message;
      if (message.targetDeviceId) {
        processedMessage = await this.encryptMessage(message, message.targetDeviceId);
      }
      
      // Add to queue
      await this.messageQueue.enqueue(processedMessage);
      
      // Track delivery
      this.deliveryTracker.trackMessage(processedMessage.id, message.targetDeviceId);
      
      // Emit event
      this.emit('messageSent', processedMessage);
      
      return processedMessage;
    } catch (error) {
      Logger.error(`MessageHandler: Failed to send message ${message.id}`, error);
      this.emit('messageError', {message, error});
      throw error;
    }
  }

  /**
   * Process received message
   */
  async processReceivedMessage(messageData, fromDeviceId) {
    try {
      // Parse message
      const message = this.parseMessage(messageData);
      
      Logger.info(`MessageHandler: Processing received message ${message.id} from ${fromDeviceId}`);
      
      // Check for duplicates
      if (this.duplicateFilter.has(message.id)) {
        Logger.debug(`MessageHandler: Duplicate message ${message.id} ignored`);
        return;
      }
      
      // Add to duplicate filter
      this.duplicateFilter.add(message.id);
      this.limitDuplicateFilter();
      
      // Validate message
      this.validateReceivedMessage(message, fromDeviceId);
      
      // Decrypt if encrypted
      let processedMessage = message;
      if (message.encrypted) {
        processedMessage = await this.decryptMessage(message, fromDeviceId);
      }
      
      // Cache message
      this.cacheMessage(processedMessage);
      
      // Check if message is for us
      const ourDeviceId = this.keyManager.getIdentityPublicKey();
      if (!processedMessage.targetDeviceId || processedMessage.targetDeviceId === ourDeviceId) {
        // Message is for us
        this.emit('messageReceived', {message: processedMessage, fromDeviceId});
        
        // Send acknowledgment
        await this.sendAcknowledgment(processedMessage.id, fromDeviceId);
      } else {
        // Message needs forwarding
        this.emit('messageForward', {message: processedMessage, fromDeviceId});
      }
      
      return processedMessage;
    } catch (error) {
      Logger.error('MessageHandler: Failed to process received message', error);
      this.emit('messageProcessingError', {messageData, fromDeviceId, error});
    }
  }

  /**
   * Encrypt message for target device
   */
  async encryptMessage(message, targetDeviceId) {
    try {
      // Get or create session with target device
      let session = this.keyManager.getSession(targetDeviceId);
      if (!session) {
        throw new Error(`No session found for device ${targetDeviceId}`);
      }
      
      // Serialize message content
      const messageContent = JSON.stringify({
        id: message.id,
        type: message.type,
        content: message.content,
        roomId: message.roomId,
        timestamp: message.timestamp,
      });
      
      // Encrypt content
      const sessionKey = Buffer.from(session.rootKey, 'base64');
      const encryptedContent = await this.encryptionEngine.encrypt(
        Buffer.from(messageContent, 'utf8'),
        sessionKey
      );
      
      // Create encrypted message
      const encryptedMessage = {
        id: message.id,
        type: MESSAGE_TYPES.ENCRYPTED_MESSAGE,
        senderId: message.senderId,
        targetDeviceId: message.targetDeviceId,
        encryptedContent: encryptedContent.toString('base64'),
        encrypted: true,
        timestamp: message.timestamp,
        ttl: message.ttl,
      };
      
      return encryptedMessage;
    } catch (error) {
      Logger.error(`MessageHandler: Failed to encrypt message for ${targetDeviceId}`, error);
      throw new Error(CRYPTO_ERRORS.ENCRYPTION_FAILED);
    }
  }

  /**
   * Decrypt received message
   */
  async decryptMessage(encryptedMessage, fromDeviceId) {
    try {
      // Get session with sender
      const session = this.keyManager.getSession(fromDeviceId);
      if (!session) {
        throw new Error(`No session found for device ${fromDeviceId}`);
      }
      
      // Decrypt content
      const sessionKey = Buffer.from(session.rootKey, 'base64');
      const encryptedContent = Buffer.from(encryptedMessage.encryptedContent, 'base64');
      
      const decryptedContent = await this.encryptionEngine.decrypt(
        encryptedContent,
        sessionKey
      );
      
      // Parse decrypted content
      const messageContent = JSON.parse(decryptedContent.toString('utf8'));
      
      // Reconstruct original message
      const decryptedMessage = {
        ...messageContent,
        senderId: encryptedMessage.senderId,
        targetDeviceId: encryptedMessage.targetDeviceId,
        encrypted: false,
        decrypted: true,
      };
      
      return decryptedMessage;
    } catch (error) {
      Logger.error(`MessageHandler: Failed to decrypt message from ${fromDeviceId}`, error);
      throw new Error(CRYPTO_ERRORS.DECRYPTION_FAILED);
    }
  }

  /**
   * Send acknowledgment for received message
   */
  async sendAcknowledgment(messageId, targetDeviceId) {
    try {
      const ackMessage = {
        id: uuidv4(),
        type: MESSAGE_TYPES.ACK,
        messageId,
        senderId: this.keyManager.getIdentityPublicKey(),
        targetDeviceId,
        timestamp: Date.now(),
      };
      
      await this.sendMessage(ackMessage);
      Logger.debug(`MessageHandler: Sent ACK for message ${messageId} to ${targetDeviceId}`);
    } catch (error) {
      Logger.error(`MessageHandler: Failed to send ACK for message ${messageId}`, error);
    }
  }

  /**
   * Handle received acknowledgment
   */
  handleAcknowledgment(ackMessage, fromDeviceId) {
    try {
      const messageId = ackMessage.messageId;
      
      // Update delivery status
      this.deliveryTracker.markDelivered(messageId, fromDeviceId);
      
      // Remove from queue if fully delivered
      if (this.deliveryTracker.isFullyDelivered(messageId)) {
        this.messageQueue.remove(messageId);
      }
      
      this.emit('messageDelivered', {messageId, fromDeviceId});
      Logger.debug(`MessageHandler: Received ACK for message ${messageId} from ${fromDeviceId}`);
    } catch (error) {
      Logger.error('MessageHandler: Failed to handle acknowledgment', error);
    }
  }

  /**
   * Validate message before sending
   */
  validateMessage(message) {
    if (!message.id) {
      throw new Error('Message ID is required');
    }
    
    if (!message.type) {
      throw new Error('Message type is required');
    }
    
    if (!message.content && message.type === MESSAGE_TYPES.TEXT) {
      throw new Error('Message content is required for text messages');
    }
    
    if (message.content && Buffer.byteLength(message.content, 'utf8') > APP_CONFIG.MESSAGING.MAX_MESSAGE_SIZE) {
      throw new Error('Message content exceeds maximum size');
    }
    
    if (!message.timestamp) {
      throw new Error('Message timestamp is required');
    }
  }

  /**
   * Validate received message
   */
  validateReceivedMessage(message, fromDeviceId) {
    // Check message age
    const age = Date.now() - message.timestamp;
    if (age > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
      throw new Error('Message is too old');
    }
    
    // Validate sender
    if (!message.senderId) {
      throw new Error('Message sender ID is required');
    }
    
    // Additional validation based on message type
    switch (message.type) {
      case MESSAGE_TYPES.TEXT:
        if (!message.content) {
          throw new Error('Text message content is required');
        }
        break;
      case MESSAGE_TYPES.ACK:
        if (!message.messageId) {
          throw new Error('ACK message must reference original message ID');
        }
        break;
    }
  }

  /**
   * Parse message from raw data
   */
  parseMessage(messageData) {
    try {
      if (Buffer.isBuffer(messageData)) {
        return JSON.parse(messageData.toString('utf8'));
      } else if (typeof messageData === 'string') {
        return JSON.parse(messageData);
      } else if (typeof messageData === 'object') {
        return messageData;
      } else {
        throw new Error('Invalid message data format');
      }
    } catch (error) {
      Logger.error('MessageHandler: Failed to parse message', error);
      throw new Error('Invalid message format');
    }
  }

  /**
   * Cache message
   */
  cacheMessage(message) {
    this.messageCache.set(message.id, {
      message,
      cachedAt: Date.now(),
    });
    
    // Limit cache size
    if (this.messageCache.size > APP_CONFIG.MESSAGING.MAX_QUEUE_SIZE) {
      const oldestKey = this.messageCache.keys().next().value;
      this.messageCache.delete(oldestKey);
    }
  }

  /**
   * Limit duplicate filter size
   */
  limitDuplicateFilter() {
    if (this.duplicateFilter.size > APP_CONFIG.MESSAGING.MAX_QUEUE_SIZE) {
      const values = Array.from(this.duplicateFilter);
      this.duplicateFilter.clear();
      
      // Keep only the most recent half
      const keepCount = Math.floor(APP_CONFIG.MESSAGING.MAX_QUEUE_SIZE / 2);
      for (let i = values.length - keepCount; i < values.length; i++) {
        this.duplicateFilter.add(values[i]);
      }
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    this.messageQueue.on('messageReady', (message) => {
      this.emit('messageReady', message);
    });
    
    this.deliveryTracker.on('deliveryTimeout', (messageId) => {
      this.emit('deliveryTimeout', messageId);
    });
  }

  /**
   * Start message processing loop
   */
  startMessageProcessing() {
    setInterval(() => {
      this.processMessageQueue();
    }, 100); // Process every 100ms
  }

  /**
   * Process queued messages
   */
  async processMessageQueue() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    try {
      const messages = await this.messageQueue.getReadyMessages();
      
      for (const message of messages) {
        try {
          if (message.type === MESSAGE_TYPES.ACK) {
            this.handleAcknowledgment(message, message.targetDeviceId);
          } else {
            this.emit('messageReady', message);
          }
        } catch (error) {
          Logger.error('MessageHandler: Failed to process queued message', error);
        }
      }
    } catch (error) {
      Logger.error('MessageHandler: Message queue processing failed', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get message statistics
   */
  getMessageStats() {
    return {
      cachedMessages: this.messageCache.size,
      duplicateFilterSize: this.duplicateFilter.size,
      queuedMessages: this.messageQueue.getQueueSize(),
      trackedDeliveries: this.deliveryTracker.getTrackedCount(),
    };
  }

  /**
   * Cleanup expired messages and data
   */
  async cleanup() {
    try {
      Logger.info('MessageHandler: Starting cleanup...');
      
      // Cleanup message cache
      const now = Date.now();
      for (const [messageId, cacheInfo] of this.messageCache) {
        if (now - cacheInfo.cachedAt > APP_CONFIG.MESSAGING.MESSAGE_TTL) {
          this.messageCache.delete(messageId);
        }
      }
      
      // Cleanup components
      await this.messageQueue.cleanup();
      await this.deliveryTracker.cleanup();
      
      Logger.info('MessageHandler: Cleanup completed');
    } catch (error) {
      Logger.error('MessageHandler: Cleanup failed', error);
    }
  }

  /**
   * Emergency cleanup (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('MessageHandler: Emergency cleanup initiated');
      
      // Clear all data
      this.messageCache.clear();
      this.duplicateFilter.clear();
      
      // Emergency cleanup components
      await this.messageQueue.emergencyCleanup();
      await this.deliveryTracker.emergencyCleanup();
      await this.keyManager.emergencyCleanup();
      
      Logger.warn('MessageHandler: Emergency cleanup completed');
    } catch (error) {
      Logger.error('MessageHandler: Emergency cleanup failed', error);
    }
  }

  /**
   * Shutdown message handler
   */
  async shutdown() {
    try {
      Logger.info('MessageHandler: Shutting down...');
      
      await this.cleanup();
      this.removeAllListeners();
      
      Logger.info('MessageHandler: Shutdown complete');
    } catch (error) {
      Logger.error('MessageHandler: Shutdown failed', error);
    }
  }
}

export default MessageHandler;
