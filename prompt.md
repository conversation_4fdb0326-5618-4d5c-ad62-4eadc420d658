https://claude.ai/chat/2624d54e-630c-4255-a756-80260d9d2725

Build the app MeshTalk - a decentralized messaging app that operates over Bluetooth mesh networks without requiring internet connection.

Detailed Requirements
CORE FUNCTIONALITY
- Bluetooth Low Energy (BLE) mesh networking
- No internet, WiFi, or cellular data required
- Zero registration: no phone numbers, emails, or accounts
- End-to-end encryption for all messages
- Peer-to-peer message relay through device mesh
- Range: 100-300 meters between devices
- Ephemeral messaging with auto-deletion
- Offline message queuing and forwarding
- Cross-platform compatibility (iOS, Android)
SECURITY & PRIVACY
- Local data storage only (no cloud servers)
- Anonymous user identification
- Panic mode: triple-tap to delete all data
- No user tracking or analytics
- No data collection or monetization
- Open-source codebase for transparency
- Forward secrecy in message encryption
- Secure key exchange protocols
USER EXPERIENCE
- Hashtag-based room creation and joining
- Password-protected private channels
- Simple, intuitive interface
- Real-time message delivery indicators
- User presence detection within range
- Message history limited to current session
- Dark mode and accessibility features
- Minimal battery consumption optimization
TECHNICAL SPECIFICATIONS
- Support for 50+ concurrent users in mesh
- Message size limit: 1KB per message
- Automatic device discovery and pairing
- Mesh network self-healing capabilities
- Future Wi-Fi Direct integration
- Media sharing (images, files) via Wi-Fi Direct
- Background operation with notifications
- Low latency message delivery (<2 seconds)
Pain Points Solved
INTERNET CENSORSHIP & SURVEILLANCE
- Bypasses government internet restrictions
- Eliminates corporate data harvesting
- Provides communication during internet blackouts
- Enables protest organization without detection
- Protects journalists and activists
NETWORK INFRASTRUCTURE FAILURES
- Works during natural disasters
- Functions in remote areas without cell towers
- Operates during network congestion
- Provides emergency communication backup
- Enables communication in underground locations
PRIVACY & SECURITY CONCERNS
- Eliminates metadata collection
- Prevents message interception by ISPs
- Removes single points of failure
- Protects against government surveillance
- Ensures true anonymity in communications
EVENT & CROWD COMMUNICATION
- Enables messaging at large gatherings
- Works in areas with overloaded networks
- Facilitates coordination during events
- Provides local community networking
- Supports temporary social networks
COST & ACCESSIBILITY
- Eliminates data plan requirements
- Reduces mobile data costs
- Works on older devices with Bluetooth
- Provides free communication alternative
- Accessible in developing regions
App Building Prompt
DEVELOPMENT BRIEF: DECENTRALIZED MESH MESSAGING APP

Create a peer-to-peer messaging application that operates exclusively through Bluetooth Low Energy mesh networks, requiring no internet connection, user registration, or central servers.

CORE ARCHITECTURE:
- Implement BLE mesh networking with automatic device discovery
- Design peer-to-peer message routing with multi-hop relay capability
- Create ephemeral messaging system with local-only storage
- Build cross-platform compatibility (iOS/Android native apps)
- Integrate end-to-end encryption with forward secrecy

SECURITY REQUIREMENTS:
- Zero user identification or tracking
- Local device storage only (no cloud integration)
- Implement panic mode for instant data deletion
- Use proven encryption protocols (Signal Protocol recommended)
- Ensure message auto-deletion after delivery
- Create secure key exchange mechanism

USER INTERFACE:
- Design minimalist, intuitive chat interface
- Implement hashtag-based room discovery system
- Add password protection for private channels
- Create real-time user presence indicators
- Include dark mode and accessibility features
- Display mesh network topology visualization

TECHNICAL CONSTRAINTS:
- Maximum range: 300 meters between devices
- Support up to 50 concurrent users in mesh
- Message size limit: 1KB per message
- Battery optimization for background operation
- Graceful degradation when devices leave mesh
- Automatic reconnection and message queuing

PRIVACY FEATURES:
- No user accounts, emails, or phone numbers
- Anonymous device identification
- No analytics or telemetry data collection
- Open-source codebase for transparency
- Configurable message retention policies
- Secure deletion of all communication traces

FUTURE ROADMAP:
- Wi-Fi Direct integration for media sharing
- Mesh network bridging capabilities
- Integration with ham radio networks
- Desktop application development
- Advanced cryptographic features

COMPLIANCE:
- Ensure regulatory compliance for BLE usage
- Implement proper app store guidelines adherence
- Create comprehensive security documentation
- Establish responsible disclosure procedures
- Design for various international privacy laws

SUCCESS METRICS:
- Mesh network stability and coverage
- Message delivery reliability (>95%)
- User retention in target scenarios
- Security audit results
- Community adoption and feedback

My github is https://github.com/HectorTa1989. Show me github readme with some good product names that nobody registered website domain with those names before, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Then code for each file in the project structure in separate artifacts (each file in 1 block) with exact file path, file name. Write commit message for each file after each file, so I can commit to github. Code using our own algorithms and free APIs is better