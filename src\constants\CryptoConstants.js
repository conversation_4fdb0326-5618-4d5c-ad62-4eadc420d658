/**
 * Cryptographic Constants
 * Encryption algorithms, key sizes, and security parameters
 */

// Encryption Algorithms
export const ENCRYPTION_ALGORITHMS = {
  AES_256_GCM: 'aes-256-gcm',
  CHACHA20_POLY1305: 'chacha20-poly1305',
  AES_256_CBC: 'aes-256-cbc',
};

// Key Exchange Algorithms
export const KEY_EXCHANGE_ALGORITHMS = {
  ECDH_P256: 'ecdh-p256',
  ECDH_P384: 'ecdh-p384',
  X25519: 'x25519',
};

// Hash Algorithms
export const HASH_ALGORITHMS = {
  SHA256: 'sha256',
  SHA512: 'sha512',
  BLAKE2B: 'blake2b',
};

// Digital Signature Algorithms
export const SIGNATURE_ALGORITHMS = {
  ECDSA_P256: 'ecdsa-p256',
  ED25519: 'ed25519',
  RSA_PSS: 'rsa-pss',
};

// Key Derivation Functions
export const KDF_ALGORITHMS = {
  HKDF_SHA256: 'hkdf-sha256',
  PBKDF2_SHA256: 'pbkdf2-sha256',
  SCRYPT: 'scrypt',
};

// Crypto Configuration
export const CRYPTO_CONFIG = {
  // Symmetric Encryption
  SYMMETRIC_KEY_SIZE: 32, // 256 bits
  IV_SIZE: 16, // 128 bits
  TAG_SIZE: 16, // 128 bits for GCM
  NONCE_SIZE: 12, // 96 bits for GCM
  
  // Asymmetric Encryption
  ECDH_KEY_SIZE: 32, // 256 bits (P-256)
  ED25519_KEY_SIZE: 32, // 256 bits
  RSA_KEY_SIZE: 2048, // 2048 bits
  
  // Key Derivation
  SALT_SIZE: 32, // 256 bits
  PBKDF2_ITERATIONS: 100000,
  SCRYPT_N: 16384,
  SCRYPT_R: 8,
  SCRYPT_P: 1,
  
  // Message Authentication
  HMAC_KEY_SIZE: 32, // 256 bits
  SIGNATURE_SIZE: 64, // 512 bits for Ed25519
  
  // Session Management
  SESSION_KEY_SIZE: 32, // 256 bits
  EPHEMERAL_KEY_SIZE: 32, // 256 bits
  PREKEY_COUNT: 100, // Number of prekeys to generate
  
  // Security Parameters
  MAX_MESSAGE_AGE: 300000, // 5 minutes
  KEY_ROTATION_INTERVAL: 86400000, // 24 hours
  SESSION_TIMEOUT: 3600000, // 1 hour
  MAX_SKIP_KEYS: 1000, // Maximum skipped message keys
};

// Protocol Constants
export const PROTOCOL_CONSTANTS = {
  // Message Types
  KEY_EXCHANGE: 0x01,
  PREKEY_BUNDLE: 0x02,
  ENCRYPTED_MESSAGE: 0x03,
  RATCHET_MESSAGE: 0x04,
  
  // Key Types
  IDENTITY_KEY: 0x10,
  EPHEMERAL_KEY: 0x11,
  PREKEY: 0x12,
  SIGNED_PREKEY: 0x13,
  ONE_TIME_PREKEY: 0x14,
  
  // Protocol Versions
  PROTOCOL_VERSION: 1,
  MIN_SUPPORTED_VERSION: 1,
  MAX_SUPPORTED_VERSION: 1,
};

// Error Codes
export const CRYPTO_ERRORS = {
  INVALID_KEY: 'INVALID_KEY',
  INVALID_SIGNATURE: 'INVALID_SIGNATURE',
  DECRYPTION_FAILED: 'DECRYPTION_FAILED',
  ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
  KEY_GENERATION_FAILED: 'KEY_GENERATION_FAILED',
  KEY_EXCHANGE_FAILED: 'KEY_EXCHANGE_FAILED',
  INVALID_MESSAGE_FORMAT: 'INVALID_MESSAGE_FORMAT',
  MESSAGE_TOO_OLD: 'MESSAGE_TOO_OLD',
  DUPLICATE_MESSAGE: 'DUPLICATE_MESSAGE',
  INVALID_PROTOCOL_VERSION: 'INVALID_PROTOCOL_VERSION',
  SESSION_NOT_FOUND: 'SESSION_NOT_FOUND',
  PREKEY_NOT_FOUND: 'PREKEY_NOT_FOUND',
  RATCHET_ERROR: 'RATCHET_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

// Security Levels
export const SECURITY_LEVELS = {
  LOW: {
    name: 'low',
    encryption: ENCRYPTION_ALGORITHMS.AES_256_CBC,
    keyExchange: KEY_EXCHANGE_ALGORITHMS.ECDH_P256,
    hash: HASH_ALGORITHMS.SHA256,
    signature: SIGNATURE_ALGORITHMS.ECDSA_P256,
    kdf: KDF_ALGORITHMS.PBKDF2_SHA256,
    iterations: 10000,
  },
  MEDIUM: {
    name: 'medium',
    encryption: ENCRYPTION_ALGORITHMS.AES_256_GCM,
    keyExchange: KEY_EXCHANGE_ALGORITHMS.ECDH_P256,
    hash: HASH_ALGORITHMS.SHA256,
    signature: SIGNATURE_ALGORITHMS.ECDSA_P256,
    kdf: KDF_ALGORITHMS.HKDF_SHA256,
    iterations: 50000,
  },
  HIGH: {
    name: 'high',
    encryption: ENCRYPTION_ALGORITHMS.CHACHA20_POLY1305,
    keyExchange: KEY_EXCHANGE_ALGORITHMS.X25519,
    hash: HASH_ALGORITHMS.SHA512,
    signature: SIGNATURE_ALGORITHMS.ED25519,
    kdf: KDF_ALGORITHMS.HKDF_SHA256,
    iterations: 100000,
  },
};

// Default Security Configuration
export const DEFAULT_SECURITY_CONFIG = SECURITY_LEVELS.HIGH;

// Message Format Constants
export const MESSAGE_FORMAT = {
  HEADER_SIZE: 32, // bytes
  VERSION_OFFSET: 0,
  TYPE_OFFSET: 1,
  FLAGS_OFFSET: 2,
  TIMESTAMP_OFFSET: 4,
  SENDER_ID_OFFSET: 12,
  RECIPIENT_ID_OFFSET: 20,
  MESSAGE_ID_OFFSET: 28,
  
  // Flags
  FLAG_ENCRYPTED: 0x01,
  FLAG_SIGNED: 0x02,
  FLAG_COMPRESSED: 0x04,
  FLAG_EPHEMERAL: 0x08,
  FLAG_RATCHET: 0x10,
};

// Key Storage Constants
export const KEY_STORAGE = {
  IDENTITY_KEY_ALIAS: 'meshtalk_identity_key',
  PREKEY_ALIAS_PREFIX: 'meshtalk_prekey_',
  SIGNED_PREKEY_ALIAS: 'meshtalk_signed_prekey',
  SESSION_KEY_PREFIX: 'meshtalk_session_',
  EPHEMERAL_KEY_PREFIX: 'meshtalk_ephemeral_',
  
  // Storage Security
  REQUIRE_AUTHENTICATION: true,
  USE_HARDWARE_SECURITY: true,
  ENCRYPT_STORAGE: true,
};

// Ratchet Constants (Double Ratchet Algorithm)
export const RATCHET_CONSTANTS = {
  ROOT_KEY_SIZE: 32,
  CHAIN_KEY_SIZE: 32,
  MESSAGE_KEY_SIZE: 32,
  MAX_SKIP: 1000,
  
  // Chain constants
  CHAIN_KEY_CONSTANT: Buffer.from([0x02]),
  MESSAGE_KEY_CONSTANT: Buffer.from([0x01]),
  
  // KDF Info strings
  ROOT_KDF_INFO: 'MeshTalk Root Key',
  CHAIN_KDF_INFO: 'MeshTalk Chain Key',
  MESSAGE_KDF_INFO: 'MeshTalk Message Key',
};

// Performance Constants
export const PERFORMANCE_CONFIG = {
  // Batch processing
  MAX_BATCH_SIZE: 10,
  BATCH_TIMEOUT: 100, // ms
  
  // Caching
  KEY_CACHE_SIZE: 100,
  SESSION_CACHE_SIZE: 50,
  MESSAGE_CACHE_SIZE: 1000,
  
  // Threading
  USE_WORKER_THREADS: false, // Not supported in React Native
  MAX_CONCURRENT_OPERATIONS: 5,
  
  // Memory management
  CLEANUP_INTERVAL: 60000, // 1 minute
  MAX_MEMORY_USAGE: 50 * 1024 * 1024, // 50MB
};

// Debug Configuration
export const DEBUG_CONFIG = {
  LOG_KEYS: false, // Never log keys in production
  LOG_MESSAGES: false, // Never log message content in production
  LOG_OPERATIONS: true,
  LOG_PERFORMANCE: true,
  VALIDATE_INPUTS: true,
  STRICT_MODE: true,
};

export default {
  ENCRYPTION_ALGORITHMS,
  KEY_EXCHANGE_ALGORITHMS,
  HASH_ALGORITHMS,
  SIGNATURE_ALGORITHMS,
  KDF_ALGORITHMS,
  CRYPTO_CONFIG,
  PROTOCOL_CONSTANTS,
  CRYPTO_ERRORS,
  SECURITY_LEVELS,
  DEFAULT_SECURITY_CONFIG,
  MESSAGE_FORMAT,
  KEY_STORAGE,
  RATCHET_CONSTANTS,
  PERFORMANCE_CONFIG,
  DEBUG_CONFIG,
};
