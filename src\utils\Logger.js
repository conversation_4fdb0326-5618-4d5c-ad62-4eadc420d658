/**
 * Logger - Centralized logging utility
 * Provides structured logging with different levels and security considerations
 */

import {APP_CONFIG} from '@constants/Config';

class Logger {
  constructor() {
    this.logLevel = APP_CONFIG.DEBUG.LOG_LEVEL;
    this.enabledLevels = this.getEnabledLevels();
    this.logHistory = [];
    this.maxHistorySize = 1000;
  }

  /**
   * Get enabled log levels based on configuration
   */
  getEnabledLevels() {
    const levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4,
    };

    const currentLevel = levels[this.logLevel] || 0;
    const enabled = [];

    for (const [level, value] of Object.entries(levels)) {
      if (value <= currentLevel) {
        enabled.push(level);
      }
    }

    return enabled;
  }

  /**
   * Check if log level is enabled
   */
  isLevelEnabled(level) {
    return this.enabledLevels.includes(level);
  }

  /**
   * Format log message
   */
  formatMessage(level, message, data = null, component = null) {
    const timestamp = new Date().toISOString();
    const componentStr = component ? `[${component}] ` : '';
    
    let formattedMessage = `${timestamp} [${level.toUpperCase()}] ${componentStr}${message}`;
    
    if (data) {
      if (data instanceof Error) {
        formattedMessage += `\nError: ${data.message}\nStack: ${data.stack}`;
      } else if (typeof data === 'object') {
        try {
          formattedMessage += `\nData: ${JSON.stringify(data, null, 2)}`;
        } catch (error) {
          formattedMessage += `\nData: [Object - JSON stringify failed]`;
        }
      } else {
        formattedMessage += `\nData: ${data}`;
      }
    }

    return formattedMessage;
  }

  /**
   * Add log entry to history
   */
  addToHistory(level, message, data, component) {
    const entry = {
      timestamp: Date.now(),
      level,
      message,
      data: this.sanitizeData(data),
      component,
    };

    this.logHistory.push(entry);

    // Limit history size
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift();
    }
  }

  /**
   * Sanitize sensitive data for logging
   */
  sanitizeData(data) {
    if (!data) return data;

    // Don't log sensitive information
    if (typeof data === 'object') {
      const sanitized = {...data};
      
      // Remove sensitive keys
      const sensitiveKeys = [
        'password', 'privateKey', 'secretKey', 'token', 'key',
        'signature', 'seed', 'mnemonic', 'pin', 'biometric'
      ];

      for (const key of sensitiveKeys) {
        if (sanitized[key]) {
          sanitized[key] = '[REDACTED]';
        }
      }

      return sanitized;
    }

    return data;
  }

  /**
   * Log error message
   */
  error(message, data = null, component = null) {
    if (!this.isLevelEnabled('error')) return;

    const formattedMessage = this.formatMessage('error', message, data, component);
    console.error(formattedMessage);
    
    this.addToHistory('error', message, data, component);
  }

  /**
   * Log warning message
   */
  warn(message, data = null, component = null) {
    if (!this.isLevelEnabled('warn')) return;

    const formattedMessage = this.formatMessage('warn', message, data, component);
    console.warn(formattedMessage);
    
    this.addToHistory('warn', message, data, component);
  }

  /**
   * Log info message
   */
  info(message, data = null, component = null) {
    if (!this.isLevelEnabled('info')) return;

    const formattedMessage = this.formatMessage('info', message, data, component);
    console.log(formattedMessage);
    
    this.addToHistory('info', message, data, component);
  }

  /**
   * Log debug message
   */
  debug(message, data = null, component = null) {
    if (!this.isLevelEnabled('debug')) return;

    const formattedMessage = this.formatMessage('debug', message, data, component);
    console.log(formattedMessage);
    
    this.addToHistory('debug', message, data, component);
  }

  /**
   * Log trace message
   */
  trace(message, data = null, component = null) {
    if (!this.isLevelEnabled('trace')) return;

    const formattedMessage = this.formatMessage('trace', message, data, component);
    console.log(formattedMessage);
    
    this.addToHistory('trace', message, data, component);
  }

  /**
   * Log network activity (if enabled)
   */
  network(message, data = null, component = 'Network') {
    if (!APP_CONFIG.DEBUG.NETWORK_LOGGING) return;
    
    this.debug(message, data, component);
  }

  /**
   * Log crypto operations (if enabled)
   */
  crypto(message, data = null, component = 'Crypto') {
    if (!APP_CONFIG.DEBUG.CRYPTO_LOGGING) return;
    
    // Extra sanitization for crypto logs
    const sanitizedData = this.sanitizeData(data);
    this.debug(message, sanitizedData, component);
  }

  /**
   * Log performance metrics
   */
  performance(message, metrics = null, component = 'Performance') {
    if (!APP_CONFIG.DEBUG.PERFORMANCE_MONITORING) return;
    
    this.info(message, metrics, component);
  }

  /**
   * Get log history
   */
  getHistory(level = null, limit = 100) {
    let history = this.logHistory;

    if (level) {
      history = history.filter(entry => entry.level === level);
    }

    return history.slice(-limit);
  }

  /**
   * Clear log history
   */
  clearHistory() {
    this.logHistory = [];
  }

  /**
   * Export logs for debugging
   */
  exportLogs() {
    const logs = {
      timestamp: new Date().toISOString(),
      appVersion: APP_CONFIG.VERSION,
      logLevel: this.logLevel,
      entries: this.logHistory,
    };

    return JSON.stringify(logs, null, 2);
  }

  /**
   * Set log level dynamically
   */
  setLogLevel(level) {
    this.logLevel = level;
    this.enabledLevels = this.getEnabledLevels();
    this.info(`Logger: Log level changed to ${level}`);
  }

  /**
   * Create component-specific logger
   */
  createComponentLogger(componentName) {
    return {
      error: (message, data) => this.error(message, data, componentName),
      warn: (message, data) => this.warn(message, data, componentName),
      info: (message, data) => this.info(message, data, componentName),
      debug: (message, data) => this.debug(message, data, componentName),
      trace: (message, data) => this.trace(message, data, componentName),
    };
  }

  /**
   * Log method entry/exit for debugging
   */
  methodEntry(className, methodName, params = null) {
    if (!this.isLevelEnabled('trace')) return;
    
    const message = `${className}.${methodName}() - Entry`;
    this.trace(message, params, className);
  }

  methodExit(className, methodName, result = null) {
    if (!this.isLevelEnabled('trace')) return;
    
    const message = `${className}.${methodName}() - Exit`;
    this.trace(message, result, className);
  }

  /**
   * Log timing information
   */
  time(label) {
    if (!this.isLevelEnabled('debug')) return;
    
    console.time(label);
  }

  timeEnd(label) {
    if (!this.isLevelEnabled('debug')) return;
    
    console.timeEnd(label);
  }

  /**
   * Log memory usage
   */
  memory(component = 'Memory') {
    if (!APP_CONFIG.DEBUG.PERFORMANCE_MONITORING) return;
    
    // React Native doesn't have process.memoryUsage()
    // This would need platform-specific implementation
    this.debug('Memory usage logging not available in React Native', null, component);
  }

  /**
   * Emergency logging (always logs regardless of level)
   */
  emergency(message, data = null, component = 'Emergency') {
    const formattedMessage = this.formatMessage('emergency', message, data, component);
    console.error(`🚨 EMERGENCY: ${formattedMessage}`);
    
    this.addToHistory('emergency', message, data, component);
  }

  /**
   * Security event logging
   */
  security(message, data = null, component = 'Security') {
    const sanitizedData = this.sanitizeData(data);
    const formattedMessage = this.formatMessage('security', message, sanitizedData, component);
    console.warn(`🔒 SECURITY: ${formattedMessage}`);
    
    this.addToHistory('security', message, sanitizedData, component);
  }
}

// Create singleton instance
const logger = new Logger();

export default logger;
