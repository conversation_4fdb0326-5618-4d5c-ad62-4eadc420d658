/**
 * PanicMode - Emergency data deletion system
 * Handles triple-tap panic mode for instant data destruction
 */

import {EventEmitter} from 'events';
import {DeviceEventEmitter, AppState} from 'react-native';
import HapticFeedback from 'react-native-haptic-feedback';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

class PanicMode extends EventEmitter {
  constructor() {
    super();
    this.isEnabled = true;
    this.tapCount = 0;
    this.lastTapTime = 0;
    this.tapTimeout = null;
    this.isActivated = false;
    this.cleanupCallbacks = new Set();
    this.appStateSubscription = null;
  }

  /**
   * Initialize panic mode
   */
  async initialize() {
    try {
      Logger.info('PanicMode: Initializing...');
      
      // Load panic mode settings
      await this.loadSettings();
      
      // Setup event listeners
      this.setupEventListeners();
      
      Logger.info('PanicMode: Initialized successfully');
    } catch (error) {
      Logger.error('PanicMode: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Register a cleanup callback
   */
  registerCleanupCallback(name, callback) {
    if (typeof callback !== 'function') {
      throw new Error('Cleanup callback must be a function');
    }
    
    this.cleanupCallbacks.add({name, callback});
    Logger.debug(`PanicMode: Registered cleanup callback: ${name}`);
  }

  /**
   * Unregister a cleanup callback
   */
  unregisterCleanupCallback(name) {
    for (const item of this.cleanupCallbacks) {
      if (item.name === name) {
        this.cleanupCallbacks.delete(item);
        Logger.debug(`PanicMode: Unregistered cleanup callback: ${name}`);
        break;
      }
    }
  }

  /**
   * Handle tap event (for triple-tap detection)
   */
  handleTap() {
    if (!this.isEnabled || this.isActivated) {
      return;
    }

    const now = Date.now();
    const timeSinceLastTap = now - this.lastTapTime;

    // Reset tap count if too much time has passed
    if (timeSinceLastTap > APP_CONFIG.SECURITY.PANIC_TAP_TIMEOUT) {
      this.tapCount = 0;
    }

    this.tapCount++;
    this.lastTapTime = now;

    Logger.debug(`PanicMode: Tap ${this.tapCount} detected`);

    // Clear existing timeout
    if (this.tapTimeout) {
      clearTimeout(this.tapTimeout);
    }

    // Check if panic threshold reached
    if (this.tapCount >= APP_CONFIG.SECURITY.PANIC_TAP_COUNT) {
      this.activatePanicMode();
    } else {
      // Provide haptic feedback for tap progress
      this.provideTapFeedback();
      
      // Set timeout to reset tap count
      this.tapTimeout = setTimeout(() => {
        this.resetTapCount();
      }, APP_CONFIG.SECURITY.PANIC_TAP_TIMEOUT);
    }
  }

  /**
   * Activate panic mode
   */
  async activatePanicMode() {
    if (this.isActivated) {
      Logger.warn('PanicMode: Already activated');
      return;
    }

    try {
      Logger.emergency('PanicMode: PANIC MODE ACTIVATED - Beginning emergency cleanup');
      
      this.isActivated = true;
      
      // Provide strong haptic feedback
      this.providePanicFeedback();
      
      // Emit activation event
      this.emit('panicActivated');
      
      // Start emergency cleanup
      await this.performEmergencyCleanup();
      
      // Emit completion event
      this.emit('panicCompleted');
      
      Logger.emergency('PanicMode: Emergency cleanup completed');
    } catch (error) {
      Logger.emergency('PanicMode: Emergency cleanup failed', error);
      this.emit('panicError', error);
    }
  }

  /**
   * Perform emergency cleanup
   */
  async performEmergencyCleanup() {
    const cleanupResults = [];
    
    Logger.emergency('PanicMode: Starting emergency cleanup procedures');
    
    // Execute all registered cleanup callbacks
    for (const {name, callback} of this.cleanupCallbacks) {
      try {
        Logger.emergency(`PanicMode: Executing cleanup: ${name}`);
        
        const startTime = Date.now();
        await callback();
        const duration = Date.now() - startTime;
        
        cleanupResults.push({
          name,
          success: true,
          duration,
        });
        
        Logger.emergency(`PanicMode: Cleanup ${name} completed in ${duration}ms`);
      } catch (error) {
        cleanupResults.push({
          name,
          success: false,
          error: error.message,
        });
        
        Logger.emergency(`PanicMode: Cleanup ${name} failed`, error);
      }
    }
    
    // Clear AsyncStorage completely
    try {
      Logger.emergency('PanicMode: Clearing AsyncStorage');
      await AsyncStorage.clear();
      cleanupResults.push({
        name: 'AsyncStorage',
        success: true,
      });
    } catch (error) {
      Logger.emergency('PanicMode: Failed to clear AsyncStorage', error);
      cleanupResults.push({
        name: 'AsyncStorage',
        success: false,
        error: error.message,
      });
    }
    
    // Clear app cache and temporary files
    try {
      Logger.emergency('PanicMode: Clearing app cache');
      await this.clearAppCache();
      cleanupResults.push({
        name: 'AppCache',
        success: true,
      });
    } catch (error) {
      Logger.emergency('PanicMode: Failed to clear app cache', error);
      cleanupResults.push({
        name: 'AppCache',
        success: false,
        error: error.message,
      });
    }
    
    // Overwrite memory (best effort)
    try {
      Logger.emergency('PanicMode: Overwriting sensitive memory');
      this.overwriteMemory();
      cleanupResults.push({
        name: 'MemoryOverwrite',
        success: true,
      });
    } catch (error) {
      Logger.emergency('PanicMode: Failed to overwrite memory', error);
      cleanupResults.push({
        name: 'MemoryOverwrite',
        success: false,
        error: error.message,
      });
    }
    
    // Log cleanup summary
    const successCount = cleanupResults.filter(r => r.success).length;
    const totalCount = cleanupResults.length;
    
    Logger.emergency(`PanicMode: Cleanup summary: ${successCount}/${totalCount} procedures completed successfully`);
    
    return cleanupResults;
  }

  /**
   * Clear app cache and temporary files
   */
  async clearAppCache() {
    // This would require platform-specific implementation
    // For now, we'll clear what we can through React Native APIs
    
    // Clear any cached images or files
    // Note: React Native doesn't provide direct cache clearing APIs
    // This would need native module implementation for complete cache clearing
    
    Logger.emergency('PanicMode: App cache clearing (limited in React Native)');
  }

  /**
   * Overwrite sensitive memory (best effort)
   */
  overwriteMemory() {
    try {
      // Create large arrays to potentially overwrite memory
      // This is a best-effort approach and not guaranteed to be effective
      const overwriteData = new Array(1000).fill(0).map(() => 
        new Array(1000).fill(Math.random().toString(36))
      );
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Clear the overwrite data
      overwriteData.length = 0;
      
      Logger.emergency('PanicMode: Memory overwrite attempt completed');
    } catch (error) {
      Logger.emergency('PanicMode: Memory overwrite failed', error);
    }
  }

  /**
   * Provide haptic feedback for tap progress
   */
  provideTapFeedback() {
    try {
      const hapticOptions = {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      };
      
      // Light feedback for tap progress
      HapticFeedback.trigger('impactLight', hapticOptions);
    } catch (error) {
      Logger.debug('PanicMode: Haptic feedback not available', error);
    }
  }

  /**
   * Provide strong haptic feedback for panic activation
   */
  providePanicFeedback() {
    try {
      const hapticOptions = {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: true,
      };
      
      // Strong feedback for panic activation
      HapticFeedback.trigger('notificationError', hapticOptions);
      
      // Additional vibration pattern
      setTimeout(() => {
        HapticFeedback.trigger('impactHeavy', hapticOptions);
      }, 200);
      
      setTimeout(() => {
        HapticFeedback.trigger('impactHeavy', hapticOptions);
      }, 400);
    } catch (error) {
      Logger.debug('PanicMode: Panic haptic feedback not available', error);
    }
  }

  /**
   * Reset tap count
   */
  resetTapCount() {
    this.tapCount = 0;
    this.lastTapTime = 0;
    
    if (this.tapTimeout) {
      clearTimeout(this.tapTimeout);
      this.tapTimeout = null;
    }
    
    Logger.debug('PanicMode: Tap count reset');
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Listen for app state changes
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Reset tap count when app goes to background
        this.resetTapCount();
      }
    });
    
    // Listen for device events (if any custom events are needed)
    // This could be extended for hardware button presses, etc.
  }

  /**
   * Enable panic mode
   */
  async enable() {
    this.isEnabled = true;
    await this.saveSettings();
    this.emit('panicModeEnabled');
    Logger.info('PanicMode: Enabled');
  }

  /**
   * Disable panic mode
   */
  async disable() {
    this.isEnabled = false;
    this.resetTapCount();
    await this.saveSettings();
    this.emit('panicModeDisabled');
    Logger.info('PanicMode: Disabled');
  }

  /**
   * Check if panic mode is enabled
   */
  isEnabledStatus() {
    return this.isEnabled;
  }

  /**
   * Check if panic mode is activated
   */
  isActivatedStatus() {
    return this.isActivated;
  }

  /**
   * Get current tap count (for testing/debugging)
   */
  getCurrentTapCount() {
    return this.tapCount;
  }

  /**
   * Load panic mode settings
   */
  async loadSettings() {
    try {
      const settings = await AsyncStorage.getItem('meshtalk_panic_settings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        this.isEnabled = parsedSettings.isEnabled !== false; // Default to enabled
      }
    } catch (error) {
      Logger.debug('PanicMode: Failed to load settings, using defaults', error);
      this.isEnabled = true; // Default to enabled
    }
  }

  /**
   * Save panic mode settings
   */
  async saveSettings() {
    try {
      const settings = {
        isEnabled: this.isEnabled,
        lastUpdated: Date.now(),
      };
      
      await AsyncStorage.setItem('meshtalk_panic_settings', JSON.stringify(settings));
    } catch (error) {
      Logger.error('PanicMode: Failed to save settings', error);
    }
  }

  /**
   * Test panic mode (for development/testing)
   */
  async testPanicMode() {
    if (__DEV__) {
      Logger.warn('PanicMode: Test activation (development mode only)');
      await this.activatePanicMode();
    } else {
      Logger.warn('PanicMode: Test activation not available in production');
    }
  }

  /**
   * Shutdown panic mode
   */
  async shutdown() {
    try {
      Logger.info('PanicMode: Shutting down...');
      
      // Reset state
      this.resetTapCount();
      
      // Remove event listeners
      if (this.appStateSubscription) {
        this.appStateSubscription.remove();
        this.appStateSubscription = null;
      }
      
      // Clear cleanup callbacks
      this.cleanupCallbacks.clear();
      
      this.removeAllListeners();
      
      Logger.info('PanicMode: Shutdown complete');
    } catch (error) {
      Logger.error('PanicMode: Shutdown failed', error);
    }
  }
}

export default PanicMode;
