/**
 * EncryptionEngine - Core encryption and decryption functionality
 * Implements AES-256-GCM and ChaCha20-Poly1305 encryption
 */

import CryptoJS from 'react-native-crypto-js';
import {randomBytes} from 'react-native-randombytes';
import {
  ENCRYPTION_ALGORITHMS,
  CRYPTO_CONFIG,
  CRYPTO_ERRORS,
  DEFAULT_SECURITY_CONFIG,
} from '@constants/CryptoConstants';
import Logger from '@utils/Logger';

class EncryptionEngine {
  constructor(securityConfig = DEFAULT_SECURITY_CONFIG) {
    this.securityConfig = securityConfig;
    this.algorithm = securityConfig.encryption;
  }

  /**
   * Encrypt data using the configured algorithm
   */
  async encrypt(data, key, additionalData = null) {
    try {
      if (!data || !key) {
        throw new Error('Data and key are required for encryption');
      }

      switch (this.algorithm) {
        case ENCRYPTION_ALGORITHMS.AES_256_GCM:
          return await this.encryptAESGCM(data, key, additionalData);
        case ENCRYPTION_ALGORITHMS.CHACHA20_POLY1305:
          return await this.encryptChaCha20Poly1305(data, key, additionalData);
        case ENCRYPTION_ALGORITHMS.AES_256_CBC:
          return await this.encryptAESCBC(data, key);
        default:
          throw new Error(`Unsupported encryption algorithm: ${this.algorithm}`);
      }
    } catch (error) {
      Logger.error('EncryptionEngine: Encryption failed', error);
      throw new Error(CRYPTO_ERRORS.ENCRYPTION_FAILED);
    }
  }

  /**
   * Decrypt data using the configured algorithm
   */
  async decrypt(encryptedData, key, additionalData = null) {
    try {
      if (!encryptedData || !key) {
        throw new Error('Encrypted data and key are required for decryption');
      }

      switch (this.algorithm) {
        case ENCRYPTION_ALGORITHMS.AES_256_GCM:
          return await this.decryptAESGCM(encryptedData, key, additionalData);
        case ENCRYPTION_ALGORITHMS.CHACHA20_POLY1305:
          return await this.decryptChaCha20Poly1305(encryptedData, key, additionalData);
        case ENCRYPTION_ALGORITHMS.AES_256_CBC:
          return await this.decryptAESCBC(encryptedData, key);
        default:
          throw new Error(`Unsupported encryption algorithm: ${this.algorithm}`);
      }
    } catch (error) {
      Logger.error('EncryptionEngine: Decryption failed', error);
      throw new Error(CRYPTO_ERRORS.DECRYPTION_FAILED);
    }
  }

  /**
   * Encrypt using AES-256-GCM
   */
  async encryptAESGCM(data, key, additionalData = null) {
    try {
      // Generate random IV
      const iv = await this.generateRandomBytes(CRYPTO_CONFIG.IV_SIZE);
      
      // Convert inputs to WordArrays
      const keyWordArray = CryptoJS.lib.WordArray.create(key);
      const dataWordArray = CryptoJS.lib.WordArray.create(data);
      const ivWordArray = CryptoJS.lib.WordArray.create(iv);
      
      // Encrypt using AES-GCM (simulated with AES-CTR + HMAC)
      const encrypted = CryptoJS.AES.encrypt(dataWordArray, keyWordArray, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CTR,
        padding: CryptoJS.pad.NoPadding,
      });
      
      // Generate authentication tag using HMAC
      const authKey = CryptoJS.HMAC(CryptoJS.SHA256, keyWordArray, CryptoJS.enc.Utf8.parse('auth'));
      let authData = ivWordArray.concat(encrypted.ciphertext);
      
      if (additionalData) {
        const additionalWordArray = CryptoJS.lib.WordArray.create(additionalData);
        authData = authData.concat(additionalWordArray);
      }
      
      const tag = CryptoJS.HMAC(CryptoJS.SHA256, authData, authKey);
      const truncatedTag = CryptoJS.lib.WordArray.create(
        tag.words.slice(0, CRYPTO_CONFIG.TAG_SIZE / 4)
      );
      
      // Combine IV + ciphertext + tag
      const result = Buffer.concat([
        Buffer.from(iv),
        Buffer.from(encrypted.ciphertext.toString(CryptoJS.enc.Base64), 'base64'),
        Buffer.from(truncatedTag.toString(CryptoJS.enc.Base64), 'base64'),
      ]);
      
      return result;
    } catch (error) {
      Logger.error('EncryptionEngine: AES-GCM encryption failed', error);
      throw error;
    }
  }

  /**
   * Decrypt using AES-256-GCM
   */
  async decryptAESGCM(encryptedData, key, additionalData = null) {
    try {
      if (encryptedData.length < CRYPTO_CONFIG.IV_SIZE + CRYPTO_CONFIG.TAG_SIZE) {
        throw new Error('Invalid encrypted data length');
      }
      
      // Extract components
      const iv = encryptedData.slice(0, CRYPTO_CONFIG.IV_SIZE);
      const tag = encryptedData.slice(-CRYPTO_CONFIG.TAG_SIZE);
      const ciphertext = encryptedData.slice(CRYPTO_CONFIG.IV_SIZE, -CRYPTO_CONFIG.TAG_SIZE);
      
      // Convert to WordArrays
      const keyWordArray = CryptoJS.lib.WordArray.create(key);
      const ivWordArray = CryptoJS.lib.WordArray.create(iv);
      const ciphertextWordArray = CryptoJS.lib.WordArray.create(ciphertext);
      const tagWordArray = CryptoJS.lib.WordArray.create(tag);
      
      // Verify authentication tag
      const authKey = CryptoJS.HMAC(CryptoJS.SHA256, keyWordArray, CryptoJS.enc.Utf8.parse('auth'));
      let authData = ivWordArray.concat(ciphertextWordArray);
      
      if (additionalData) {
        const additionalWordArray = CryptoJS.lib.WordArray.create(additionalData);
        authData = authData.concat(additionalWordArray);
      }
      
      const expectedTag = CryptoJS.HMAC(CryptoJS.SHA256, authData, authKey);
      const truncatedExpectedTag = CryptoJS.lib.WordArray.create(
        expectedTag.words.slice(0, CRYPTO_CONFIG.TAG_SIZE / 4)
      );
      
      if (!this.constantTimeEquals(tagWordArray, truncatedExpectedTag)) {
        throw new Error('Authentication tag verification failed');
      }
      
      // Decrypt
      const decrypted = CryptoJS.AES.decrypt(
        {ciphertext: ciphertextWordArray},
        keyWordArray,
        {
          iv: ivWordArray,
          mode: CryptoJS.mode.CTR,
          padding: CryptoJS.pad.NoPadding,
        }
      );
      
      return Buffer.from(decrypted.toString(CryptoJS.enc.Base64), 'base64');
    } catch (error) {
      Logger.error('EncryptionEngine: AES-GCM decryption failed', error);
      throw error;
    }
  }

  /**
   * Encrypt using ChaCha20-Poly1305 (simplified implementation)
   */
  async encryptChaCha20Poly1305(data, key, additionalData = null) {
    // Note: This is a simplified implementation
    // In production, use a proper ChaCha20-Poly1305 library
    Logger.warn('EncryptionEngine: Using AES-GCM fallback for ChaCha20-Poly1305');
    return await this.encryptAESGCM(data, key, additionalData);
  }

  /**
   * Decrypt using ChaCha20-Poly1305 (simplified implementation)
   */
  async decryptChaCha20Poly1305(encryptedData, key, additionalData = null) {
    // Note: This is a simplified implementation
    // In production, use a proper ChaCha20-Poly1305 library
    Logger.warn('EncryptionEngine: Using AES-GCM fallback for ChaCha20-Poly1305');
    return await this.decryptAESGCM(encryptedData, key, additionalData);
  }

  /**
   * Encrypt using AES-256-CBC
   */
  async encryptAESCBC(data, key) {
    try {
      // Generate random IV
      const iv = await this.generateRandomBytes(CRYPTO_CONFIG.IV_SIZE);
      
      // Convert inputs to WordArrays
      const keyWordArray = CryptoJS.lib.WordArray.create(key);
      const dataWordArray = CryptoJS.lib.WordArray.create(data);
      const ivWordArray = CryptoJS.lib.WordArray.create(iv);
      
      // Encrypt
      const encrypted = CryptoJS.AES.encrypt(dataWordArray, keyWordArray, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      
      // Combine IV + ciphertext
      const result = Buffer.concat([
        Buffer.from(iv),
        Buffer.from(encrypted.ciphertext.toString(CryptoJS.enc.Base64), 'base64'),
      ]);
      
      return result;
    } catch (error) {
      Logger.error('EncryptionEngine: AES-CBC encryption failed', error);
      throw error;
    }
  }

  /**
   * Decrypt using AES-256-CBC
   */
  async decryptAESCBC(encryptedData, key) {
    try {
      if (encryptedData.length < CRYPTO_CONFIG.IV_SIZE) {
        throw new Error('Invalid encrypted data length');
      }
      
      // Extract IV and ciphertext
      const iv = encryptedData.slice(0, CRYPTO_CONFIG.IV_SIZE);
      const ciphertext = encryptedData.slice(CRYPTO_CONFIG.IV_SIZE);
      
      // Convert to WordArrays
      const keyWordArray = CryptoJS.lib.WordArray.create(key);
      const ivWordArray = CryptoJS.lib.WordArray.create(iv);
      const ciphertextWordArray = CryptoJS.lib.WordArray.create(ciphertext);
      
      // Decrypt
      const decrypted = CryptoJS.AES.decrypt(
        {ciphertext: ciphertextWordArray},
        keyWordArray,
        {
          iv: ivWordArray,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
        }
      );
      
      return Buffer.from(decrypted.toString(CryptoJS.enc.Base64), 'base64');
    } catch (error) {
      Logger.error('EncryptionEngine: AES-CBC decryption failed', error);
      throw error;
    }
  }

  /**
   * Generate random bytes
   */
  async generateRandomBytes(size) {
    return new Promise((resolve, reject) => {
      randomBytes(size, (error, bytes) => {
        if (error) {
          reject(error);
        } else {
          resolve(bytes);
        }
      });
    });
  }

  /**
   * Generate symmetric key
   */
  async generateSymmetricKey() {
    return await this.generateRandomBytes(CRYPTO_CONFIG.SYMMETRIC_KEY_SIZE);
  }

  /**
   * Derive key from password using PBKDF2
   */
  async deriveKeyFromPassword(password, salt, iterations = CRYPTO_CONFIG.PBKDF2_ITERATIONS) {
    try {
      const passwordWordArray = CryptoJS.enc.Utf8.parse(password);
      const saltWordArray = CryptoJS.lib.WordArray.create(salt);
      
      const derivedKey = CryptoJS.PBKDF2(passwordWordArray, saltWordArray, {
        keySize: CRYPTO_CONFIG.SYMMETRIC_KEY_SIZE / 4, // WordArray size is in 32-bit words
        iterations: iterations,
        hasher: CryptoJS.algo.SHA256,
      });
      
      return Buffer.from(derivedKey.toString(CryptoJS.enc.Base64), 'base64');
    } catch (error) {
      Logger.error('EncryptionEngine: Key derivation failed', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Derive key using HKDF
   */
  async deriveKeyHKDF(inputKeyMaterial, salt, info, length = CRYPTO_CONFIG.SYMMETRIC_KEY_SIZE) {
    try {
      // HKDF Extract
      const saltWordArray = salt ? CryptoJS.lib.WordArray.create(salt) : CryptoJS.lib.WordArray.create();
      const ikmWordArray = CryptoJS.lib.WordArray.create(inputKeyMaterial);
      const prk = CryptoJS.HMAC(CryptoJS.SHA256, ikmWordArray, saltWordArray);
      
      // HKDF Expand
      const infoWordArray = info ? CryptoJS.enc.Utf8.parse(info) : CryptoJS.lib.WordArray.create();
      const n = Math.ceil(length / 32); // SHA256 output size is 32 bytes
      let okm = CryptoJS.lib.WordArray.create();
      let t = CryptoJS.lib.WordArray.create();
      
      for (let i = 1; i <= n; i++) {
        const input = t.concat(infoWordArray).concat(CryptoJS.lib.WordArray.create([i]));
        t = CryptoJS.HMAC(CryptoJS.SHA256, input, prk);
        okm = okm.concat(t);
      }
      
      // Truncate to desired length
      const result = CryptoJS.lib.WordArray.create(okm.words.slice(0, length / 4));
      return Buffer.from(result.toString(CryptoJS.enc.Base64), 'base64');
    } catch (error) {
      Logger.error('EncryptionEngine: HKDF derivation failed', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Constant time comparison to prevent timing attacks
   */
  constantTimeEquals(a, b) {
    if (a.sigBytes !== b.sigBytes) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.words.length; i++) {
      result |= a.words[i] ^ b.words[i];
    }
    
    return result === 0;
  }

  /**
   * Secure memory cleanup (best effort)
   */
  secureCleanup(data) {
    if (Buffer.isBuffer(data)) {
      data.fill(0);
    } else if (data && data.words) {
      // CryptoJS WordArray
      for (let i = 0; i < data.words.length; i++) {
        data.words[i] = 0;
      }
    }
  }

  /**
   * Get algorithm information
   */
  getAlgorithmInfo() {
    return {
      algorithm: this.algorithm,
      keySize: CRYPTO_CONFIG.SYMMETRIC_KEY_SIZE,
      ivSize: CRYPTO_CONFIG.IV_SIZE,
      tagSize: CRYPTO_CONFIG.TAG_SIZE,
      securityLevel: this.securityConfig.name,
    };
  }
}

export default EncryptionEngine;
