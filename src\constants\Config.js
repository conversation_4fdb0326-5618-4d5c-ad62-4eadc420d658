/**
 * MeshTalk Configuration Constants
 * Central configuration for the entire application
 */

export const APP_CONFIG = {
  // Application Information
  APP_NAME: 'MeshTalk',
  VERSION: '1.0.0',
  BUILD_NUMBER: 1,
  
  // Mesh Network Configuration
  MESH: {
    MAX_DEVICES: 50,
    MAX_HOPS: 5,
    DISCOVERY_TIMEOUT: 30000, // 30 seconds
    CONNECTION_TIMEOUT: 15000, // 15 seconds
    HEARTBEAT_INTERVAL: 10000, // 10 seconds
    MESH_REFRESH_INTERVAL: 5000, // 5 seconds
    MAX_RETRY_ATTEMPTS: 3,
    SIGNAL_STRENGTH_THRESHOLD: -80, // dBm
  },
  
  // Message Configuration
  MESSAGING: {
    MAX_MESSAGE_SIZE: 1024, // 1KB
    MAX_QUEUE_SIZE: 100,
    MESSAGE_TTL: 300000, // 5 minutes
    DELIVERY_TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 3,
    BATCH_SIZE: 10,
  },
  
  // Security Configuration
  SECURITY: {
    KEY_SIZE: 256, // bits
    SALT_SIZE: 32, // bytes
    IV_SIZE: 16, // bytes
    SESSION_TIMEOUT: 3600000, // 1 hour
    KEY_ROTATION_INTERVAL: 86400000, // 24 hours
    PANIC_TAP_COUNT: 3,
    PANIC_TAP_TIMEOUT: 2000, // 2 seconds
  },
  
  // Storage Configuration
  STORAGE: {
    MAX_MESSAGES: 1000,
    CLEANUP_INTERVAL: 60000, // 1 minute
    BACKUP_INTERVAL: 300000, // 5 minutes
    ENCRYPTION_ENABLED: true,
  },
  
  // UI Configuration
  UI: {
    THEME: 'dark',
    ANIMATION_DURATION: 300,
    TOAST_DURATION: 3000,
    REFRESH_RATE: 60, // FPS
    MAX_VISIBLE_MESSAGES: 50,
  },
  
  // Performance Configuration
  PERFORMANCE: {
    BATTERY_OPTIMIZATION: true,
    BACKGROUND_SYNC: true,
    LOW_POWER_MODE_THRESHOLD: 20, // %
    CPU_THROTTLE_THRESHOLD: 80, // %
    MEMORY_WARNING_THRESHOLD: 100, // MB
  },
  
  // Debug Configuration
  DEBUG: {
    ENABLED: __DEV__,
    LOG_LEVEL: __DEV__ ? 'debug' : 'error',
    NETWORK_LOGGING: __DEV__,
    CRYPTO_LOGGING: false,
    PERFORMANCE_MONITORING: true,
  },
};

export const ROOM_CONFIG = {
  MAX_ROOM_NAME_LENGTH: 50,
  MAX_ROOM_DESCRIPTION_LENGTH: 200,
  MAX_PARTICIPANTS: 50,
  DEFAULT_ROOMS: [
    '#general',
    '#help',
    '#tech',
    '#random',
  ],
};

export const USER_CONFIG = {
  MAX_USERNAME_LENGTH: 20,
  MIN_USERNAME_LENGTH: 3,
  ANONYMOUS_PREFIX: 'User',
  STATUS_UPDATE_INTERVAL: 30000, // 30 seconds
  PRESENCE_TIMEOUT: 60000, // 1 minute
};

export default APP_CONFIG;
