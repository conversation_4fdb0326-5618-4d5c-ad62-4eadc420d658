/**
 * ChatScreen - Main chat interface
 * Displays messages and provides input for sending new messages
 */

import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import MessageBubble from './MessageBubble';
import UserPresence from './UserPresence';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

const ChatScreen = ({meshManager, messageHandler, messageStore}) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [currentRoom, setCurrentRoom] = useState('#general');
  const [connectedDevices, setConnectedDevices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const flatListRef = useRef(null);

  useEffect(() => {
    initializeChat();
    setupEventListeners();
    
    return () => {
      cleanupEventListeners();
    };
  }, []);

  useEffect(() => {
    loadRoomMessages();
  }, [currentRoom]);

  /**
   * Initialize chat screen
   */
  const initializeChat = async () => {
    try {
      setIsLoading(true);
      
      // Load recent messages
      await loadRoomMessages();
      
      // Get connected devices
      updateConnectedDevices();
      
      setIsLoading(false);
    } catch (error) {
      Logger.error('ChatScreen: Initialization failed', error);
      setIsLoading(false);
    }
  };

  /**
   * Setup event listeners
   */
  const setupEventListeners = () => {
    if (messageHandler) {
      messageHandler.on('messageReceived', handleMessageReceived);
      messageHandler.on('messageSent', handleMessageSent);
      messageHandler.on('messageError', handleMessageError);
    }
    
    if (meshManager) {
      meshManager.on('deviceConnected', handleDeviceConnected);
      meshManager.on('deviceDisconnected', handleDeviceDisconnected);
      meshManager.on('topologyUpdated', handleTopologyUpdated);
    }
  };

  /**
   * Cleanup event listeners
   */
  const cleanupEventListeners = () => {
    if (messageHandler) {
      messageHandler.removeListener('messageReceived', handleMessageReceived);
      messageHandler.removeListener('messageSent', handleMessageSent);
      messageHandler.removeListener('messageError', handleMessageError);
    }
    
    if (meshManager) {
      meshManager.removeListener('deviceConnected', handleDeviceConnected);
      meshManager.removeListener('deviceDisconnected', handleDeviceDisconnected);
      meshManager.removeListener('topologyUpdated', handleTopologyUpdated);
    }
  };

  /**
   * Load messages for current room
   */
  const loadRoomMessages = async () => {
    try {
      if (!messageStore) return;
      
      const roomMessages = await messageStore.getMessagesByRoom(currentRoom, 50);
      setMessages(roomMessages);
      
      // Scroll to bottom
      setTimeout(() => {
        if (flatListRef.current && roomMessages.length > 0) {
          flatListRef.current.scrollToEnd({animated: true});
        }
      }, 100);
      
    } catch (error) {
      Logger.error('ChatScreen: Failed to load room messages', error);
    }
  };

  /**
   * Handle received message
   */
  const handleMessageReceived = ({message, fromDeviceId}) => {
    try {
      // Only show messages for current room or direct messages
      if (message.roomId === currentRoom || !message.roomId) {
        setMessages(prevMessages => {
          // Check if message already exists
          const exists = prevMessages.some(m => m.id === message.id);
          if (exists) return prevMessages;
          
          // Add new message
          const newMessages = [...prevMessages, message];
          
          // Sort by timestamp
          newMessages.sort((a, b) => a.timestamp - b.timestamp);
          
          // Limit message count
          if (newMessages.length > APP_CONFIG.UI.MAX_VISIBLE_MESSAGES) {
            return newMessages.slice(-APP_CONFIG.UI.MAX_VISIBLE_MESSAGES);
          }
          
          return newMessages;
        });
        
        // Auto-scroll to bottom for new messages
        setTimeout(() => {
          if (flatListRef.current) {
            flatListRef.current.scrollToEnd({animated: true});
          }
        }, 100);
      }
    } catch (error) {
      Logger.error('ChatScreen: Failed to handle received message', error);
    }
  };

  /**
   * Handle sent message
   */
  const handleMessageSent = (message) => {
    try {
      // Message should already be in the list from sending
      Logger.debug('ChatScreen: Message sent successfully', message.id);
    } catch (error) {
      Logger.error('ChatScreen: Failed to handle sent message', error);
    }
  };

  /**
   * Handle message error
   */
  const handleMessageError = ({message, error}) => {
    try {
      Alert.alert(
        'Message Error',
        `Failed to send message: ${error.message}`,
        [
          {
            text: 'Retry',
            onPress: () => retryMessage(message),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    } catch (err) {
      Logger.error('ChatScreen: Failed to handle message error', err);
    }
  };

  /**
   * Handle device connected
   */
  const handleDeviceConnected = (device) => {
    updateConnectedDevices();
  };

  /**
   * Handle device disconnected
   */
  const handleDeviceDisconnected = (deviceId) => {
    updateConnectedDevices();
  };

  /**
   * Handle topology updated
   */
  const handleTopologyUpdated = (topology) => {
    updateConnectedDevices();
  };

  /**
   * Update connected devices list
   */
  const updateConnectedDevices = () => {
    try {
      if (meshManager) {
        const devices = meshManager.getNetworkTopology().nodes || [];
        setConnectedDevices(devices);
      }
    } catch (error) {
      Logger.error('ChatScreen: Failed to update connected devices', error);
    }
  };

  /**
   * Send message
   */
  const sendMessage = async () => {
    try {
      if (!inputText.trim() || !messageHandler) {
        return;
      }

      const messageText = inputText.trim();
      setInputText('');

      // Send message
      const message = await messageHandler.sendTextMessage(
        messageText,
        null, // No specific target (broadcast to room)
        currentRoom
      );

      // Add to local messages immediately for better UX
      setMessages(prevMessages => {
        const newMessages = [...prevMessages, message];
        newMessages.sort((a, b) => a.timestamp - b.timestamp);
        
        if (newMessages.length > APP_CONFIG.UI.MAX_VISIBLE_MESSAGES) {
          return newMessages.slice(-APP_CONFIG.UI.MAX_VISIBLE_MESSAGES);
        }
        
        return newMessages;
      });

      // Auto-scroll to bottom
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToEnd({animated: true});
        }
      }, 100);

    } catch (error) {
      Logger.error('ChatScreen: Failed to send message', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  /**
   * Retry sending a message
   */
  const retryMessage = async (message) => {
    try {
      if (messageHandler) {
        await messageHandler.sendMessage(message);
      }
    } catch (error) {
      Logger.error('ChatScreen: Failed to retry message', error);
      Alert.alert('Error', 'Failed to retry message.');
    }
  };

  /**
   * Render message item
   */
  const renderMessage = ({item}) => (
    <MessageBubble
      message={item}
      isOwnMessage={item.senderId === meshManager?.getDeviceId()}
    />
  );

  /**
   * Render header with room info and user presence
   */
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.roomInfo}>
        <Text style={styles.roomName}>{currentRoom}</Text>
        <Text style={styles.deviceCount}>
          {connectedDevices.length} device{connectedDevices.length !== 1 ? 's' : ''} connected
        </Text>
      </View>
      <UserPresence devices={connectedDevices} />
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {renderHeader()}
      
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => {
          if (flatListRef.current) {
            flatListRef.current.scrollToEnd({animated: false});
          }
        }}
      />
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Type a message..."
          placeholderTextColor="#666"
          multiline
          maxLength={APP_CONFIG.MESSAGING.MAX_MESSAGE_SIZE}
          onSubmitEditing={sendMessage}
          returnKeyType="send"
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            {opacity: inputText.trim() ? 1 : 0.5}
          ]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    backgroundColor: '#2a2a2a',
  },
  roomInfo: {
    flex: 1,
  },
  roomName: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  deviceCount: {
    color: '#666',
    fontSize: 12,
    marginTop: 2,
  },
  messagesList: {
    flex: 1,
  },
  messagesContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#333',
    backgroundColor: '#2a2a2a',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#444',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    color: '#fff',
    backgroundColor: '#333',
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#00ff88',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default ChatScreen;
