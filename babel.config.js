module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: ['.ios.js', '.android.js', '.js', '.jsx', '.json'],
        alias: {
          '@': './src',
          '@bluetooth': './src/bluetooth',
          '@crypto': './src/crypto',
          '@messaging': './src/messaging',
          '@storage': './src/storage',
          '@components': './src/components',
          '@utils': './src/utils',
          '@constants': './src/constants',
        },
      },
    ],
  ],
};
