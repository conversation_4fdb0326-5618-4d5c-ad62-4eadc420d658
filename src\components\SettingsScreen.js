/**
 * SettingsScreen - App configuration and settings
 * Provides access to app settings, panic mode, and system information
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Share,
} from 'react-native';
import {APP_CONFIG} from '@constants/Config';
import Logger from '@utils/Logger';

const SettingsScreen = ({meshManager, messageHandler, messageStore, panicMode}) => {
  const [panicModeEnabled, setPanicModeEnabled] = useState(true);
  const [bluetoothEnabled, setBluetoothEnabled] = useState(false);
  const [storageStats, setStorageStats] = useState(null);
  const [networkStats, setNetworkStats] = useState(null);

  useEffect(() => {
    loadSettings();
    loadStats();
  }, []);

  /**
   * Load current settings
   */
  const loadSettings = async () => {
    try {
      if (panicMode) {
        setPanicModeEnabled(panicMode.isEnabledStatus());
      }
      
      if (meshManager) {
        const stats = meshManager.getNetworkStats();
        setBluetoothEnabled(stats.isScanning || stats.isAdvertising);
      }
    } catch (error) {
      Logger.error('SettingsScreen: Failed to load settings', error);
    }
  };

  /**
   * Load statistics
   */
  const loadStats = async () => {
    try {
      if (messageStore) {
        const storage = await messageStore.getStorageStats();
        setStorageStats(storage);
      }
      
      if (meshManager) {
        const network = meshManager.getNetworkStats();
        setNetworkStats(network);
      }
    } catch (error) {
      Logger.error('SettingsScreen: Failed to load stats', error);
    }
  };

  /**
   * Toggle panic mode
   */
  const togglePanicMode = async (enabled) => {
    try {
      if (panicMode) {
        if (enabled) {
          await panicMode.enable();
        } else {
          await panicMode.disable();
        }
        setPanicModeEnabled(enabled);
      }
    } catch (error) {
      Logger.error('SettingsScreen: Failed to toggle panic mode', error);
      Alert.alert('Error', 'Failed to update panic mode setting');
    }
  };

  /**
   * Test panic mode
   */
  const testPanicMode = () => {
    Alert.alert(
      'Test Panic Mode',
      'This will trigger emergency cleanup and delete all data. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Test',
          style: 'destructive',
          onPress: () => {
            if (panicMode) {
              panicMode.testPanicMode();
            }
          },
        },
      ]
    );
  };

  /**
   * Clear all data
   */
  const clearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'This will delete all messages, rooms, and settings. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              if (messageStore) await messageStore.emergencyCleanup();
              if (messageHandler) await messageHandler.emergencyCleanup();
              
              Alert.alert('Success', 'All data has been cleared');
              await loadStats();
            } catch (error) {
              Logger.error('SettingsScreen: Failed to clear data', error);
              Alert.alert('Error', 'Failed to clear all data');
            }
          },
        },
      ]
    );
  };

  /**
   * Export logs
   */
  const exportLogs = async () => {
    try {
      const logs = Logger.exportLogs();
      
      await Share.share({
        message: logs,
        title: 'MeshTalk Debug Logs',
      });
    } catch (error) {
      Logger.error('SettingsScreen: Failed to export logs', error);
      Alert.alert('Error', 'Failed to export logs');
    }
  };

  /**
   * Render setting item
   */
  const renderSettingItem = (title, subtitle, value, onToggle, type = 'switch') => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && (
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        )}
      </View>
      
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{false: '#333', true: '#00ff88'}}
          thumbColor={value ? '#fff' : '#666'}
        />
      )}
      
      {type === 'button' && (
        <TouchableOpacity
          style={styles.settingButton}
          onPress={onToggle}
        >
          <Text style={styles.settingButtonText}>{value}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  /**
   * Render statistics section
   */
  const renderStatistics = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Statistics</Text>
      
      {storageStats && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>Storage</Text>
          <Text style={styles.statItem}>
            Messages: {storageStats.totalMessages || 0}
          </Text>
          <Text style={styles.statItem}>
            Cached: {storageStats.cachedMessages || 0}
          </Text>
          <Text style={styles.statItem}>
            Rooms: {storageStats.roomCount || 0}
          </Text>
          <Text style={styles.statItem}>
            Size: {formatBytes(storageStats.totalSize || 0)}
          </Text>
        </View>
      )}
      
      {networkStats && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>Network</Text>
          <Text style={styles.statItem}>
            Connected Devices: {networkStats.nodeCount || 0}
          </Text>
          <Text style={styles.statItem}>
            Scanning: {networkStats.isScanning ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.statItem}>
            Advertising: {networkStats.isAdvertising ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.statItem}>
            Routes: {networkStats.routingTableSize || 0}
          </Text>
        </View>
      )}
    </View>
  );

  /**
   * Format bytes to human readable
   */
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Security Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security</Text>
        
        {renderSettingItem(
          'Panic Mode',
          'Triple-tap to instantly delete all data',
          panicModeEnabled,
          togglePanicMode
        )}
        
        {__DEV__ && renderSettingItem(
          'Test Panic Mode',
          'Test emergency cleanup (development only)',
          'Test',
          testPanicMode,
          'button'
        )}
      </View>
      
      {/* Network Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Network</Text>
        
        {renderSettingItem(
          'Bluetooth Mesh',
          bluetoothEnabled ? 'Active and discovering devices' : 'Inactive',
          bluetoothEnabled,
          () => {}, // Read-only for now
          'switch'
        )}
      </View>
      
      {/* Data Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data Management</Text>
        
        {renderSettingItem(
          'Clear All Data',
          'Delete all messages and settings',
          'Clear',
          clearAllData,
          'button'
        )}
        
        {renderSettingItem(
          'Export Logs',
          'Share debug logs for troubleshooting',
          'Export',
          exportLogs,
          'button'
        )}
      </View>
      
      {/* Statistics */}
      {renderStatistics()}
      
      {/* App Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>About</Text>
        
        <View style={styles.infoContainer}>
          <Text style={styles.infoItem}>
            App Version: {APP_CONFIG.VERSION}
          </Text>
          <Text style={styles.infoItem}>
            Build: {APP_CONFIG.BUILD_NUMBER}
          </Text>
          <Text style={styles.infoItem}>
            Protocol Version: 1.0.0
          </Text>
          <Text style={styles.infoItem}>
            GitHub: github.com/HectorTa1989/MeshTalk
          </Text>
        </View>
        
        <Text style={styles.disclaimer}>
          MeshTalk is open-source software for decentralized communication. 
          Use responsibly and in compliance with local laws.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingSubtitle: {
    color: '#666',
    fontSize: 12,
    lineHeight: 16,
  },
  settingButton: {
    backgroundColor: '#00ff88',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  settingButtonText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
  },
  statsContainer: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  statsTitle: {
    color: '#00ff88',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statItem: {
    color: '#ccc',
    fontSize: 12,
    marginBottom: 4,
  },
  infoContainer: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  infoItem: {
    color: '#ccc',
    fontSize: 12,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  disclaimer: {
    color: '#666',
    fontSize: 11,
    lineHeight: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default SettingsScreen;
