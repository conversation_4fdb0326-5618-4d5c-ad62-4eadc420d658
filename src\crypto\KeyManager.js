/**
 * KeyManager - Cryptographic key management and storage
 * Handles key generation, storage, rotation, and lifecycle management
 */

import {randomBytes} from 'react-native-randombytes';
import Keychain from 'react-native-keychain';
import CryptoJS from 'react-native-crypto-js';
import {
  CRYPTO_CONFIG,
  KEY_STORAGE,
  CRYPTO_ERRORS,
  PROTOCOL_CONSTANTS,
} from '@constants/CryptoConstants';
import EncryptionEngine from './EncryptionEngine';
import Logger from '@utils/Logger';

class KeyManager {
  constructor() {
    this.encryptionEngine = new EncryptionEngine();
    this.keyCache = new Map(); // In-memory key cache
    this.identityKeyPair = null;
    this.signedPreKey = null;
    this.preKeys = new Map();
    this.sessions = new Map();
  }

  /**
   * Initialize key manager
   */
  async initialize() {
    try {
      Logger.info('KeyManager: Initializing...');
      
      // Load or generate identity key
      await this.loadOrGenerateIdentityKey();
      
      // Load or generate signed prekey
      await this.loadOrGenerateSignedPreKey();
      
      // Load or generate one-time prekeys
      await this.loadOrGeneratePreKeys();
      
      // Load existing sessions
      await this.loadSessions();
      
      Logger.info('KeyManager: Initialized successfully');
    } catch (error) {
      Logger.error('KeyManager: Initialization failed', error);
      throw error;
    }
  }

  /**
   * Generate or load identity key pair
   */
  async loadOrGenerateIdentityKey() {
    try {
      // Try to load existing identity key
      const storedKey = await this.getStoredKey(KEY_STORAGE.IDENTITY_KEY_ALIAS);
      
      if (storedKey) {
        this.identityKeyPair = JSON.parse(storedKey);
        Logger.info('KeyManager: Loaded existing identity key');
      } else {
        // Generate new identity key pair
        this.identityKeyPair = await this.generateKeyPair();
        await this.storeKey(KEY_STORAGE.IDENTITY_KEY_ALIAS, JSON.stringify(this.identityKeyPair));
        Logger.info('KeyManager: Generated new identity key');
      }
    } catch (error) {
      Logger.error('KeyManager: Failed to load/generate identity key', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Generate or load signed prekey
   */
  async loadOrGenerateSignedPreKey() {
    try {
      const storedKey = await this.getStoredKey(KEY_STORAGE.SIGNED_PREKEY_ALIAS);
      
      if (storedKey && !this.isKeyExpired(JSON.parse(storedKey))) {
        this.signedPreKey = JSON.parse(storedKey);
        Logger.info('KeyManager: Loaded existing signed prekey');
      } else {
        // Generate new signed prekey
        const keyPair = await this.generateKeyPair();
        const signature = await this.signData(keyPair.publicKey, this.identityKeyPair.privateKey);
        
        this.signedPreKey = {
          id: Date.now(),
          keyPair,
          signature,
          timestamp: Date.now(),
        };
        
        await this.storeKey(KEY_STORAGE.SIGNED_PREKEY_ALIAS, JSON.stringify(this.signedPreKey));
        Logger.info('KeyManager: Generated new signed prekey');
      }
    } catch (error) {
      Logger.error('KeyManager: Failed to load/generate signed prekey', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Generate or load one-time prekeys
   */
  async loadOrGeneratePreKeys() {
    try {
      // Load existing prekeys
      for (let i = 0; i < CRYPTO_CONFIG.PREKEY_COUNT; i++) {
        const alias = `${KEY_STORAGE.PREKEY_ALIAS_PREFIX}${i}`;
        const storedKey = await this.getStoredKey(alias);
        
        if (storedKey) {
          const preKey = JSON.parse(storedKey);
          this.preKeys.set(preKey.id, preKey);
        }
      }
      
      // Generate missing prekeys
      const missingCount = CRYPTO_CONFIG.PREKEY_COUNT - this.preKeys.size;
      if (missingCount > 0) {
        await this.generatePreKeys(missingCount);
        Logger.info(`KeyManager: Generated ${missingCount} new prekeys`);
      } else {
        Logger.info(`KeyManager: Loaded ${this.preKeys.size} existing prekeys`);
      }
    } catch (error) {
      Logger.error('KeyManager: Failed to load/generate prekeys', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Generate multiple one-time prekeys
   */
  async generatePreKeys(count) {
    for (let i = 0; i < count; i++) {
      const keyPair = await this.generateKeyPair();
      const preKey = {
        id: Date.now() + i,
        keyPair,
        timestamp: Date.now(),
      };
      
      this.preKeys.set(preKey.id, preKey);
      
      const alias = `${KEY_STORAGE.PREKEY_ALIAS_PREFIX}${preKey.id}`;
      await this.storeKey(alias, JSON.stringify(preKey));
    }
  }

  /**
   * Generate ECDH key pair (simplified implementation)
   */
  async generateKeyPair() {
    try {
      // Generate private key (32 random bytes for Curve25519)
      const privateKey = await this.generateRandomBytes(CRYPTO_CONFIG.ECDH_KEY_SIZE);
      
      // Generate public key (simplified - in production use proper curve25519)
      const publicKey = await this.derivePublicKey(privateKey);
      
      return {
        privateKey: privateKey.toString('base64'),
        publicKey: publicKey.toString('base64'),
      };
    } catch (error) {
      Logger.error('KeyManager: Key pair generation failed', error);
      throw new Error(CRYPTO_ERRORS.KEY_GENERATION_FAILED);
    }
  }

  /**
   * Derive public key from private key (simplified implementation)
   */
  async derivePublicKey(privateKey) {
    // This is a simplified implementation
    // In production, use proper Curve25519 or P-256 implementation
    const hash = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(privateKey));
    return Buffer.from(hash.toString(CryptoJS.enc.Base64), 'base64');
  }

  /**
   * Perform ECDH key exchange
   */
  async performKeyExchange(ourPrivateKey, theirPublicKey) {
    try {
      // Simplified ECDH implementation
      // In production, use proper curve25519 or P-256 ECDH
      const ourPrivateKeyBuffer = Buffer.from(ourPrivateKey, 'base64');
      const theirPublicKeyBuffer = Buffer.from(theirPublicKey, 'base64');
      
      // Combine keys and hash (simplified)
      const combined = Buffer.concat([ourPrivateKeyBuffer, theirPublicKeyBuffer]);
      const sharedSecret = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(combined));
      
      return Buffer.from(sharedSecret.toString(CryptoJS.enc.Base64), 'base64');
    } catch (error) {
      Logger.error('KeyManager: Key exchange failed', error);
      throw new Error(CRYPTO_ERRORS.KEY_EXCHANGE_FAILED);
    }
  }

  /**
   * Sign data with private key
   */
  async signData(data, privateKey) {
    try {
      const privateKeyBuffer = Buffer.from(privateKey, 'base64');
      const dataBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8');
      
      // Simplified signing (in production use Ed25519 or ECDSA)
      const combined = Buffer.concat([privateKeyBuffer, dataBuffer]);
      const signature = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(combined));
      
      return signature.toString(CryptoJS.enc.Base64);
    } catch (error) {
      Logger.error('KeyManager: Data signing failed', error);
      throw new Error(CRYPTO_ERRORS.INVALID_SIGNATURE);
    }
  }

  /**
   * Verify signature
   */
  async verifySignature(data, signature, publicKey) {
    try {
      const publicKeyBuffer = Buffer.from(publicKey, 'base64');
      const dataBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8');
      
      // Simplified verification (in production use Ed25519 or ECDSA)
      const combined = Buffer.concat([publicKeyBuffer, dataBuffer]);
      const expectedSignature = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(combined));
      
      return signature === expectedSignature.toString(CryptoJS.enc.Base64);
    } catch (error) {
      Logger.error('KeyManager: Signature verification failed', error);
      return false;
    }
  }

  /**
   * Create session with another device
   */
  async createSession(deviceId, theirIdentityKey, theirSignedPreKey, theirOneTimePreKey = null) {
    try {
      // Verify signed prekey signature
      const signatureValid = await this.verifySignature(
        theirSignedPreKey.keyPair.publicKey,
        theirSignedPreKey.signature,
        theirIdentityKey
      );
      
      if (!signatureValid) {
        throw new Error('Invalid signed prekey signature');
      }
      
      // Generate ephemeral key pair
      const ephemeralKeyPair = await this.generateKeyPair();
      
      // Perform multiple key exchanges (X3DH protocol)
      const dh1 = await this.performKeyExchange(
        this.identityKeyPair.privateKey,
        theirSignedPreKey.keyPair.publicKey
      );
      
      const dh2 = await this.performKeyExchange(
        ephemeralKeyPair.privateKey,
        theirIdentityKey
      );
      
      const dh3 = await this.performKeyExchange(
        ephemeralKeyPair.privateKey,
        theirSignedPreKey.keyPair.publicKey
      );
      
      let dh4 = null;
      if (theirOneTimePreKey) {
        dh4 = await this.performKeyExchange(
          ephemeralKeyPair.privateKey,
          theirOneTimePreKey.keyPair.publicKey
        );
      }
      
      // Derive root key
      const keyMaterial = dh4 
        ? Buffer.concat([dh1, dh2, dh3, dh4])
        : Buffer.concat([dh1, dh2, dh3]);
      
      const rootKey = await this.encryptionEngine.deriveKeyHKDF(
        keyMaterial,
        null,
        'MeshTalk Root Key'
      );
      
      // Create session
      const session = {
        deviceId,
        rootKey: rootKey.toString('base64'),
        sendingChainKey: null,
        receivingChainKey: null,
        ephemeralKeyPair,
        theirIdentityKey,
        theirCurrentKey: theirSignedPreKey.keyPair.publicKey,
        messageKeys: new Map(),
        sendMessageNumber: 0,
        receiveMessageNumber: 0,
        previousSendingChainLength: 0,
        createdAt: Date.now(),
      };
      
      this.sessions.set(deviceId, session);
      await this.storeSession(deviceId, session);
      
      Logger.info(`KeyManager: Created session with device ${deviceId}`);
      return session;
    } catch (error) {
      Logger.error(`KeyManager: Failed to create session with ${deviceId}`, error);
      throw new Error(CRYPTO_ERRORS.KEY_EXCHANGE_FAILED);
    }
  }

  /**
   * Get prekey bundle for key exchange
   */
  getPreKeyBundle() {
    const oneTimePreKey = this.getAvailablePreKey();
    
    return {
      identityKey: this.identityKeyPair.publicKey,
      signedPreKey: {
        id: this.signedPreKey.id,
        keyPair: {
          publicKey: this.signedPreKey.keyPair.publicKey,
        },
        signature: this.signedPreKey.signature,
      },
      oneTimePreKey: oneTimePreKey ? {
        id: oneTimePreKey.id,
        keyPair: {
          publicKey: oneTimePreKey.keyPair.publicKey,
        },
      } : null,
    };
  }

  /**
   * Get available one-time prekey
   */
  getAvailablePreKey() {
    const preKeyIds = Array.from(this.preKeys.keys());
    if (preKeyIds.length === 0) return null;
    
    const preKeyId = preKeyIds[0];
    const preKey = this.preKeys.get(preKeyId);
    
    // Remove used prekey
    this.preKeys.delete(preKeyId);
    this.removeStoredKey(`${KEY_STORAGE.PREKEY_ALIAS_PREFIX}${preKeyId}`);
    
    return preKey;
  }

  /**
   * Store key securely
   */
  async storeKey(alias, keyData) {
    try {
      await Keychain.setInternetCredentials(
        alias,
        alias,
        keyData,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
          authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
          accessGroup: 'MeshTalk',
          storage: Keychain.STORAGE_TYPE.KC,
        }
      );
    } catch (error) {
      Logger.error(`KeyManager: Failed to store key ${alias}`, error);
      throw error;
    }
  }

  /**
   * Retrieve stored key
   */
  async getStoredKey(alias) {
    try {
      const credentials = await Keychain.getInternetCredentials(alias);
      return credentials ? credentials.password : null;
    } catch (error) {
      Logger.debug(`KeyManager: Key ${alias} not found or inaccessible`);
      return null;
    }
  }

  /**
   * Remove stored key
   */
  async removeStoredKey(alias) {
    try {
      await Keychain.resetInternetCredentials(alias);
    } catch (error) {
      Logger.error(`KeyManager: Failed to remove key ${alias}`, error);
    }
  }

  /**
   * Store session
   */
  async storeSession(deviceId, session) {
    const alias = `${KEY_STORAGE.SESSION_KEY_PREFIX}${deviceId}`;
    await this.storeKey(alias, JSON.stringify(session));
  }

  /**
   * Load sessions
   */
  async loadSessions() {
    // This would require enumerating stored sessions
    // For now, sessions are loaded on-demand
    Logger.info('KeyManager: Session loading not implemented (loaded on-demand)');
  }

  /**
   * Check if key is expired
   */
  isKeyExpired(keyData) {
    if (!keyData.timestamp) return false;
    
    const age = Date.now() - keyData.timestamp;
    return age > CRYPTO_CONFIG.KEY_ROTATION_INTERVAL;
  }

  /**
   * Generate random bytes
   */
  async generateRandomBytes(size) {
    return new Promise((resolve, reject) => {
      randomBytes(size, (error, bytes) => {
        if (error) {
          reject(error);
        } else {
          resolve(bytes);
        }
      });
    });
  }

  /**
   * Get identity public key
   */
  getIdentityPublicKey() {
    return this.identityKeyPair ? this.identityKeyPair.publicKey : null;
  }

  /**
   * Get session
   */
  getSession(deviceId) {
    return this.sessions.get(deviceId);
  }

  /**
   * Cleanup expired keys and sessions
   */
  async cleanup() {
    try {
      Logger.info('KeyManager: Starting cleanup...');
      
      // Cleanup expired prekeys
      const expiredPreKeys = [];
      for (const [id, preKey] of this.preKeys) {
        if (this.isKeyExpired(preKey)) {
          expiredPreKeys.push(id);
        }
      }
      
      for (const id of expiredPreKeys) {
        this.preKeys.delete(id);
        await this.removeStoredKey(`${KEY_STORAGE.PREKEY_ALIAS_PREFIX}${id}`);
      }
      
      // Rotate signed prekey if expired
      if (this.isKeyExpired(this.signedPreKey)) {
        await this.loadOrGenerateSignedPreKey();
      }
      
      // Generate new prekeys if needed
      const missingCount = CRYPTO_CONFIG.PREKEY_COUNT - this.preKeys.size;
      if (missingCount > 0) {
        await this.generatePreKeys(missingCount);
      }
      
      Logger.info('KeyManager: Cleanup completed');
    } catch (error) {
      Logger.error('KeyManager: Cleanup failed', error);
    }
  }

  /**
   * Emergency key deletion (panic mode)
   */
  async emergencyCleanup() {
    try {
      Logger.warn('KeyManager: Emergency cleanup initiated');
      
      // Clear in-memory keys
      this.identityKeyPair = null;
      this.signedPreKey = null;
      this.preKeys.clear();
      this.sessions.clear();
      this.keyCache.clear();
      
      // Remove all stored keys
      await this.removeStoredKey(KEY_STORAGE.IDENTITY_KEY_ALIAS);
      await this.removeStoredKey(KEY_STORAGE.SIGNED_PREKEY_ALIAS);
      
      // Remove prekeys
      for (let i = 0; i < CRYPTO_CONFIG.PREKEY_COUNT; i++) {
        await this.removeStoredKey(`${KEY_STORAGE.PREKEY_ALIAS_PREFIX}${i}`);
      }
      
      Logger.warn('KeyManager: Emergency cleanup completed');
    } catch (error) {
      Logger.error('KeyManager: Emergency cleanup failed', error);
    }
  }
}

export default KeyManager;
